package grpc

import (
	v1 "resflow/proto/generated_go/v1"
)

type NodeDefinitionGrpcService interface {
	v1.NodeDefinitionServiceServer
}

type nodeDefinitionGrpcService struct {
	v1.UnimplementedNodeDefinitionServiceServer
	//service node_definition.Service
}

func NewNodeDefinitionGrpcService() NodeDefinitionGrpcService {
	return &nodeDefinitionGrpcService{}
}

//
//func (s *nodeDefinitionGrpcService) List(ctx context.Context, req *v1.ListNodeDefinitionsRequest) (*v1.ListNodeDefinitionsResponse, error) {
//	nodeDefinitions, err := s.service.List(ctx)
//	if err != nil {
//		return nil, err
//	}
//
//	nodeDefinitionsPb := convertDTOsToPb(nodeDefinitions)
//
//	return &v1.ListNodeDefinitionsResponse{NodeDefinitions: nodeDefinitionsPb}, nil
//}
//
//func (s *nodeDefinitionGrpcService) GetByTypeAndVersion(ctx context.Context, req *v1.GetNodeDefinitionByTypeAndVersionRequest) (*v1.GetNodeDefinitionByTypeAndVersionResponse, error) {
//	validate, trans := common.GetValidate()
//
//	if errs := validate.Struct(*req); errs != nil {
//		return nil, status.Error(codes.InvalidArgument, common.GetValidationErrMsg(errs.(validator.ValidationErrors), trans, *req))
//	}
//
//	nodeDefinition, err := s.service.GetByTypeAndVersion(ctx, req.GetType(), req.GetVersion())
//	if err != nil {
//		return nil, err
//	}
//
//	return &v1.GetNodeDefinitionByTypeAndVersionResponse{NodeDefinition: convertDTOToPb(nodeDefinition)}, nil
//}
//
//func convertDTOToPb(nodeDefinitions *node_definition.NodeDefinitionDTO) *v1.NodeDefinition {
//	return &v1.NodeDefinition{
//		Id:           nodeDefinitions.ID,
//		Name:         nodeDefinitions.Name,
//		Author:       nodeDefinitions.Author,
//		Description:  nodeDefinitions.Description,
//		Icon:         nodeDefinitions.Icon,
//		Type:         nodeDefinitions.Type,
//		Version:      nodeDefinitions.Version,
//		Category:     nodeDefinitions.Category,
//		InputParams:  nodeDefinitions.InputParams,
//		OutputParams: nodeDefinitions.OutputParams,
//		InputPorts:   nodeDefinitions.InputPorts,
//		OutputPorts:  nodeDefinitions.OutputPorts,
//		Exception:    nodeDefinitions.Exception,
//		Builtin:      nodeDefinitions.Builtin,
//		Enabled:      nodeDefinitions.Enabled,
//	}
//}
//
//func convertDTOsToPb(nodeDefinitions []*node_definition.NodeDefinitionDTO) []*v1.NodeDefinition {
//	nodeDefinitionsPb := make([]*v1.NodeDefinition, 0)
//	for _, nodeDefinition := range nodeDefinitions {
//		nodeDefinitionsPb = append(nodeDefinitionsPb, convertDTOToPb(nodeDefinition))
//	}
//	return nodeDefinitionsPb
//}
