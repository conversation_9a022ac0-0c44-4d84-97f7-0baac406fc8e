package grpc

import (
	v1 "resflow/proto/generated_go/v1"
)

type PluginGrpcService interface {
	v1.PluginServiceServer
}

type pluginGrpcService struct {
	v1.UnimplementedPluginServiceServer
}

func NewPluginGrpcService() PluginGrpcService {
	return &pluginGrpcService{}
}

//
//func (ps *pluginGrpcService) List(ctx context.Context, request *v1.ListPluginsRequest) (*v1.ListPluginsResponse, error) {
//	plugins, err := ps.service.List(ctx)
//	if err != nil {
//		return nil, err
//	}
//
//	for _, pluginDTO := range plugins {
//		pluginDTO.Icon = "plugin/icon/" + pluginDTO.Name + "/" + pluginDTO.Version + "/" + pluginDTO.Icon
//	}
//
//	pbPlugins := convertPluginToPb(plugins)
//
//	return &v1.ListPluginsResponse{Plugins: pbPlugins}, nil
//}
//
//func convertPluginToPb(plugins []*dto.PluginDTO) []*v1.Plugin {
//	pbPlugins := make([]*v1.Plugin, 0)
//	for _, pluginDTO := range plugins {
//		pbPlugins = append(pbPlugins, &v1.Plugin{
//			Id:          pluginDTO.ID,
//			Name:        pluginDTO.Name,
//			Version:     pluginDTO.Version,
//			Author:      pluginDTO.Author,
//			DisplayName: pluginDTO.DisplayName,
//			Description: pluginDTO.Description,
//			Icon:        pluginDTO.Icon,
//			Path:        pluginDTO.Path,
//			Builtin:     pluginDTO.Builtin,
//			Enabled:     pluginDTO.Enabled,
//			CreatedAt:   pluginDTO.CreatedAt.Format(time.DateTime),
//			UpdatedAt:   pluginDTO.UpdatedAt.Format(time.DateTime),
//		})
//	}
//	return pbPlugins
//}
