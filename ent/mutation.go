// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"resflow/ent/nodedefinition"
	"resflow/ent/plugin"
	"resflow/ent/predicate"
	"resflow/ent/user"
	"resflow/ent/workflow"
	"resflow/ent/workflowlink"
	"resflow/ent/workflownode"
	"resflow/enums"
	"resflow/internal/plugin/domain"
	v1 "resflow/proto/generated_go/v1"
	"sync"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
)

const (
	// Operation types.
	OpCreate    = ent.OpCreate
	OpDelete    = ent.OpDelete
	OpDeleteOne = ent.OpDeleteOne
	OpUpdate    = ent.OpUpdate
	OpUpdateOne = ent.OpUpdateOne

	// Node types.
	TypeNodeDefinition = "NodeDefinition"
	TypePlugin         = "Plugin"
	TypeUser           = "User"
	TypeWorkflow       = "Workflow"
	TypeWorkflowLink   = "WorkflowLink"
	TypeWorkflowNode   = "WorkflowNode"
)

// NodeDefinitionMutation represents an operation that mutates the NodeDefinition nodes in the graph.
type NodeDefinitionMutation struct {
	config
	op                  Op
	typ                 string
	id                  *uuid.UUID
	plugin_name         *string
	name                *string
	author              *string
	description         *string
	icon                *string
	_type               *string
	version             *string
	category            *string
	input_params        *[]*domain.NodeParam
	appendinput_params  []*domain.NodeParam
	output_params       *[]*domain.NodeParam
	appendoutput_params []*domain.NodeParam
	input_ports         *[]*domain.NodePort
	appendinput_ports   []*domain.NodePort
	output_ports        *[]*domain.NodePort
	appendoutput_ports  []*domain.NodePort
	exception           *bool
	_path               *string
	builtin             *bool
	enabled             *bool
	created_at          *time.Time
	updated_at          *time.Time
	clearedFields       map[string]struct{}
	done                bool
	oldValue            func(context.Context) (*NodeDefinition, error)
	predicates          []predicate.NodeDefinition
}

var _ ent.Mutation = (*NodeDefinitionMutation)(nil)

// nodedefinitionOption allows management of the mutation configuration using functional options.
type nodedefinitionOption func(*NodeDefinitionMutation)

// newNodeDefinitionMutation creates new mutation for the NodeDefinition entity.
func newNodeDefinitionMutation(c config, op Op, opts ...nodedefinitionOption) *NodeDefinitionMutation {
	m := &NodeDefinitionMutation{
		config:        c,
		op:            op,
		typ:           TypeNodeDefinition,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withNodeDefinitionID sets the ID field of the mutation.
func withNodeDefinitionID(id uuid.UUID) nodedefinitionOption {
	return func(m *NodeDefinitionMutation) {
		var (
			err   error
			once  sync.Once
			value *NodeDefinition
		)
		m.oldValue = func(ctx context.Context) (*NodeDefinition, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().NodeDefinition.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withNodeDefinition sets the old NodeDefinition of the mutation.
func withNodeDefinition(node *NodeDefinition) nodedefinitionOption {
	return func(m *NodeDefinitionMutation) {
		m.oldValue = func(context.Context) (*NodeDefinition, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m NodeDefinitionMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m NodeDefinitionMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of NodeDefinition entities.
func (m *NodeDefinitionMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *NodeDefinitionMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *NodeDefinitionMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().NodeDefinition.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetPluginName sets the "plugin_name" field.
func (m *NodeDefinitionMutation) SetPluginName(s string) {
	m.plugin_name = &s
}

// PluginName returns the value of the "plugin_name" field in the mutation.
func (m *NodeDefinitionMutation) PluginName() (r string, exists bool) {
	v := m.plugin_name
	if v == nil {
		return
	}
	return *v, true
}

// OldPluginName returns the old "plugin_name" field's value of the NodeDefinition entity.
// If the NodeDefinition object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NodeDefinitionMutation) OldPluginName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPluginName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPluginName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPluginName: %w", err)
	}
	return oldValue.PluginName, nil
}

// ResetPluginName resets all changes to the "plugin_name" field.
func (m *NodeDefinitionMutation) ResetPluginName() {
	m.plugin_name = nil
}

// SetName sets the "name" field.
func (m *NodeDefinitionMutation) SetName(s string) {
	m.name = &s
}

// Name returns the value of the "name" field in the mutation.
func (m *NodeDefinitionMutation) Name() (r string, exists bool) {
	v := m.name
	if v == nil {
		return
	}
	return *v, true
}

// OldName returns the old "name" field's value of the NodeDefinition entity.
// If the NodeDefinition object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NodeDefinitionMutation) OldName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldName: %w", err)
	}
	return oldValue.Name, nil
}

// ResetName resets all changes to the "name" field.
func (m *NodeDefinitionMutation) ResetName() {
	m.name = nil
}

// SetAuthor sets the "author" field.
func (m *NodeDefinitionMutation) SetAuthor(s string) {
	m.author = &s
}

// Author returns the value of the "author" field in the mutation.
func (m *NodeDefinitionMutation) Author() (r string, exists bool) {
	v := m.author
	if v == nil {
		return
	}
	return *v, true
}

// OldAuthor returns the old "author" field's value of the NodeDefinition entity.
// If the NodeDefinition object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NodeDefinitionMutation) OldAuthor(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldAuthor is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldAuthor requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldAuthor: %w", err)
	}
	return oldValue.Author, nil
}

// ResetAuthor resets all changes to the "author" field.
func (m *NodeDefinitionMutation) ResetAuthor() {
	m.author = nil
}

// SetDescription sets the "description" field.
func (m *NodeDefinitionMutation) SetDescription(s string) {
	m.description = &s
}

// Description returns the value of the "description" field in the mutation.
func (m *NodeDefinitionMutation) Description() (r string, exists bool) {
	v := m.description
	if v == nil {
		return
	}
	return *v, true
}

// OldDescription returns the old "description" field's value of the NodeDefinition entity.
// If the NodeDefinition object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NodeDefinitionMutation) OldDescription(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDescription is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDescription requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDescription: %w", err)
	}
	return oldValue.Description, nil
}

// ResetDescription resets all changes to the "description" field.
func (m *NodeDefinitionMutation) ResetDescription() {
	m.description = nil
}

// SetIcon sets the "icon" field.
func (m *NodeDefinitionMutation) SetIcon(s string) {
	m.icon = &s
}

// Icon returns the value of the "icon" field in the mutation.
func (m *NodeDefinitionMutation) Icon() (r string, exists bool) {
	v := m.icon
	if v == nil {
		return
	}
	return *v, true
}

// OldIcon returns the old "icon" field's value of the NodeDefinition entity.
// If the NodeDefinition object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NodeDefinitionMutation) OldIcon(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIcon is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIcon requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIcon: %w", err)
	}
	return oldValue.Icon, nil
}

// ResetIcon resets all changes to the "icon" field.
func (m *NodeDefinitionMutation) ResetIcon() {
	m.icon = nil
}

// SetType sets the "type" field.
func (m *NodeDefinitionMutation) SetType(s string) {
	m._type = &s
}

// GetType returns the value of the "type" field in the mutation.
func (m *NodeDefinitionMutation) GetType() (r string, exists bool) {
	v := m._type
	if v == nil {
		return
	}
	return *v, true
}

// OldType returns the old "type" field's value of the NodeDefinition entity.
// If the NodeDefinition object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NodeDefinitionMutation) OldType(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldType: %w", err)
	}
	return oldValue.Type, nil
}

// ResetType resets all changes to the "type" field.
func (m *NodeDefinitionMutation) ResetType() {
	m._type = nil
}

// SetVersion sets the "version" field.
func (m *NodeDefinitionMutation) SetVersion(s string) {
	m.version = &s
}

// Version returns the value of the "version" field in the mutation.
func (m *NodeDefinitionMutation) Version() (r string, exists bool) {
	v := m.version
	if v == nil {
		return
	}
	return *v, true
}

// OldVersion returns the old "version" field's value of the NodeDefinition entity.
// If the NodeDefinition object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NodeDefinitionMutation) OldVersion(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldVersion is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldVersion requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldVersion: %w", err)
	}
	return oldValue.Version, nil
}

// ResetVersion resets all changes to the "version" field.
func (m *NodeDefinitionMutation) ResetVersion() {
	m.version = nil
}

// SetCategory sets the "category" field.
func (m *NodeDefinitionMutation) SetCategory(s string) {
	m.category = &s
}

// Category returns the value of the "category" field in the mutation.
func (m *NodeDefinitionMutation) Category() (r string, exists bool) {
	v := m.category
	if v == nil {
		return
	}
	return *v, true
}

// OldCategory returns the old "category" field's value of the NodeDefinition entity.
// If the NodeDefinition object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NodeDefinitionMutation) OldCategory(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCategory is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCategory requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCategory: %w", err)
	}
	return oldValue.Category, nil
}

// ResetCategory resets all changes to the "category" field.
func (m *NodeDefinitionMutation) ResetCategory() {
	m.category = nil
}

// SetInputParams sets the "input_params" field.
func (m *NodeDefinitionMutation) SetInputParams(dp []*domain.NodeParam) {
	m.input_params = &dp
	m.appendinput_params = nil
}

// InputParams returns the value of the "input_params" field in the mutation.
func (m *NodeDefinitionMutation) InputParams() (r []*domain.NodeParam, exists bool) {
	v := m.input_params
	if v == nil {
		return
	}
	return *v, true
}

// OldInputParams returns the old "input_params" field's value of the NodeDefinition entity.
// If the NodeDefinition object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NodeDefinitionMutation) OldInputParams(ctx context.Context) (v []*domain.NodeParam, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldInputParams is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldInputParams requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldInputParams: %w", err)
	}
	return oldValue.InputParams, nil
}

// AppendInputParams adds dp to the "input_params" field.
func (m *NodeDefinitionMutation) AppendInputParams(dp []*domain.NodeParam) {
	m.appendinput_params = append(m.appendinput_params, dp...)
}

// AppendedInputParams returns the list of values that were appended to the "input_params" field in this mutation.
func (m *NodeDefinitionMutation) AppendedInputParams() ([]*domain.NodeParam, bool) {
	if len(m.appendinput_params) == 0 {
		return nil, false
	}
	return m.appendinput_params, true
}

// ResetInputParams resets all changes to the "input_params" field.
func (m *NodeDefinitionMutation) ResetInputParams() {
	m.input_params = nil
	m.appendinput_params = nil
}

// SetOutputParams sets the "output_params" field.
func (m *NodeDefinitionMutation) SetOutputParams(dp []*domain.NodeParam) {
	m.output_params = &dp
	m.appendoutput_params = nil
}

// OutputParams returns the value of the "output_params" field in the mutation.
func (m *NodeDefinitionMutation) OutputParams() (r []*domain.NodeParam, exists bool) {
	v := m.output_params
	if v == nil {
		return
	}
	return *v, true
}

// OldOutputParams returns the old "output_params" field's value of the NodeDefinition entity.
// If the NodeDefinition object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NodeDefinitionMutation) OldOutputParams(ctx context.Context) (v []*domain.NodeParam, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldOutputParams is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldOutputParams requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldOutputParams: %w", err)
	}
	return oldValue.OutputParams, nil
}

// AppendOutputParams adds dp to the "output_params" field.
func (m *NodeDefinitionMutation) AppendOutputParams(dp []*domain.NodeParam) {
	m.appendoutput_params = append(m.appendoutput_params, dp...)
}

// AppendedOutputParams returns the list of values that were appended to the "output_params" field in this mutation.
func (m *NodeDefinitionMutation) AppendedOutputParams() ([]*domain.NodeParam, bool) {
	if len(m.appendoutput_params) == 0 {
		return nil, false
	}
	return m.appendoutput_params, true
}

// ResetOutputParams resets all changes to the "output_params" field.
func (m *NodeDefinitionMutation) ResetOutputParams() {
	m.output_params = nil
	m.appendoutput_params = nil
}

// SetInputPorts sets the "input_ports" field.
func (m *NodeDefinitionMutation) SetInputPorts(dp []*domain.NodePort) {
	m.input_ports = &dp
	m.appendinput_ports = nil
}

// InputPorts returns the value of the "input_ports" field in the mutation.
func (m *NodeDefinitionMutation) InputPorts() (r []*domain.NodePort, exists bool) {
	v := m.input_ports
	if v == nil {
		return
	}
	return *v, true
}

// OldInputPorts returns the old "input_ports" field's value of the NodeDefinition entity.
// If the NodeDefinition object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NodeDefinitionMutation) OldInputPorts(ctx context.Context) (v []*domain.NodePort, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldInputPorts is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldInputPorts requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldInputPorts: %w", err)
	}
	return oldValue.InputPorts, nil
}

// AppendInputPorts adds dp to the "input_ports" field.
func (m *NodeDefinitionMutation) AppendInputPorts(dp []*domain.NodePort) {
	m.appendinput_ports = append(m.appendinput_ports, dp...)
}

// AppendedInputPorts returns the list of values that were appended to the "input_ports" field in this mutation.
func (m *NodeDefinitionMutation) AppendedInputPorts() ([]*domain.NodePort, bool) {
	if len(m.appendinput_ports) == 0 {
		return nil, false
	}
	return m.appendinput_ports, true
}

// ResetInputPorts resets all changes to the "input_ports" field.
func (m *NodeDefinitionMutation) ResetInputPorts() {
	m.input_ports = nil
	m.appendinput_ports = nil
}

// SetOutputPorts sets the "output_ports" field.
func (m *NodeDefinitionMutation) SetOutputPorts(dp []*domain.NodePort) {
	m.output_ports = &dp
	m.appendoutput_ports = nil
}

// OutputPorts returns the value of the "output_ports" field in the mutation.
func (m *NodeDefinitionMutation) OutputPorts() (r []*domain.NodePort, exists bool) {
	v := m.output_ports
	if v == nil {
		return
	}
	return *v, true
}

// OldOutputPorts returns the old "output_ports" field's value of the NodeDefinition entity.
// If the NodeDefinition object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NodeDefinitionMutation) OldOutputPorts(ctx context.Context) (v []*domain.NodePort, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldOutputPorts is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldOutputPorts requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldOutputPorts: %w", err)
	}
	return oldValue.OutputPorts, nil
}

// AppendOutputPorts adds dp to the "output_ports" field.
func (m *NodeDefinitionMutation) AppendOutputPorts(dp []*domain.NodePort) {
	m.appendoutput_ports = append(m.appendoutput_ports, dp...)
}

// AppendedOutputPorts returns the list of values that were appended to the "output_ports" field in this mutation.
func (m *NodeDefinitionMutation) AppendedOutputPorts() ([]*domain.NodePort, bool) {
	if len(m.appendoutput_ports) == 0 {
		return nil, false
	}
	return m.appendoutput_ports, true
}

// ResetOutputPorts resets all changes to the "output_ports" field.
func (m *NodeDefinitionMutation) ResetOutputPorts() {
	m.output_ports = nil
	m.appendoutput_ports = nil
}

// SetException sets the "exception" field.
func (m *NodeDefinitionMutation) SetException(b bool) {
	m.exception = &b
}

// Exception returns the value of the "exception" field in the mutation.
func (m *NodeDefinitionMutation) Exception() (r bool, exists bool) {
	v := m.exception
	if v == nil {
		return
	}
	return *v, true
}

// OldException returns the old "exception" field's value of the NodeDefinition entity.
// If the NodeDefinition object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NodeDefinitionMutation) OldException(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldException is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldException requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldException: %w", err)
	}
	return oldValue.Exception, nil
}

// ResetException resets all changes to the "exception" field.
func (m *NodeDefinitionMutation) ResetException() {
	m.exception = nil
}

// SetPath sets the "path" field.
func (m *NodeDefinitionMutation) SetPath(s string) {
	m._path = &s
}

// Path returns the value of the "path" field in the mutation.
func (m *NodeDefinitionMutation) Path() (r string, exists bool) {
	v := m._path
	if v == nil {
		return
	}
	return *v, true
}

// OldPath returns the old "path" field's value of the NodeDefinition entity.
// If the NodeDefinition object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NodeDefinitionMutation) OldPath(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPath is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPath requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPath: %w", err)
	}
	return oldValue.Path, nil
}

// ResetPath resets all changes to the "path" field.
func (m *NodeDefinitionMutation) ResetPath() {
	m._path = nil
}

// SetBuiltin sets the "builtin" field.
func (m *NodeDefinitionMutation) SetBuiltin(b bool) {
	m.builtin = &b
}

// Builtin returns the value of the "builtin" field in the mutation.
func (m *NodeDefinitionMutation) Builtin() (r bool, exists bool) {
	v := m.builtin
	if v == nil {
		return
	}
	return *v, true
}

// OldBuiltin returns the old "builtin" field's value of the NodeDefinition entity.
// If the NodeDefinition object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NodeDefinitionMutation) OldBuiltin(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldBuiltin is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldBuiltin requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldBuiltin: %w", err)
	}
	return oldValue.Builtin, nil
}

// ResetBuiltin resets all changes to the "builtin" field.
func (m *NodeDefinitionMutation) ResetBuiltin() {
	m.builtin = nil
}

// SetEnabled sets the "enabled" field.
func (m *NodeDefinitionMutation) SetEnabled(b bool) {
	m.enabled = &b
}

// Enabled returns the value of the "enabled" field in the mutation.
func (m *NodeDefinitionMutation) Enabled() (r bool, exists bool) {
	v := m.enabled
	if v == nil {
		return
	}
	return *v, true
}

// OldEnabled returns the old "enabled" field's value of the NodeDefinition entity.
// If the NodeDefinition object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NodeDefinitionMutation) OldEnabled(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldEnabled is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldEnabled requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldEnabled: %w", err)
	}
	return oldValue.Enabled, nil
}

// ResetEnabled resets all changes to the "enabled" field.
func (m *NodeDefinitionMutation) ResetEnabled() {
	m.enabled = nil
}

// SetCreatedAt sets the "created_at" field.
func (m *NodeDefinitionMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *NodeDefinitionMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the NodeDefinition entity.
// If the NodeDefinition object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NodeDefinitionMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *NodeDefinitionMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *NodeDefinitionMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *NodeDefinitionMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the NodeDefinition entity.
// If the NodeDefinition object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *NodeDefinitionMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *NodeDefinitionMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// Where appends a list predicates to the NodeDefinitionMutation builder.
func (m *NodeDefinitionMutation) Where(ps ...predicate.NodeDefinition) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the NodeDefinitionMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *NodeDefinitionMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.NodeDefinition, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *NodeDefinitionMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *NodeDefinitionMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (NodeDefinition).
func (m *NodeDefinitionMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *NodeDefinitionMutation) Fields() []string {
	fields := make([]string, 0, 18)
	if m.plugin_name != nil {
		fields = append(fields, nodedefinition.FieldPluginName)
	}
	if m.name != nil {
		fields = append(fields, nodedefinition.FieldName)
	}
	if m.author != nil {
		fields = append(fields, nodedefinition.FieldAuthor)
	}
	if m.description != nil {
		fields = append(fields, nodedefinition.FieldDescription)
	}
	if m.icon != nil {
		fields = append(fields, nodedefinition.FieldIcon)
	}
	if m._type != nil {
		fields = append(fields, nodedefinition.FieldType)
	}
	if m.version != nil {
		fields = append(fields, nodedefinition.FieldVersion)
	}
	if m.category != nil {
		fields = append(fields, nodedefinition.FieldCategory)
	}
	if m.input_params != nil {
		fields = append(fields, nodedefinition.FieldInputParams)
	}
	if m.output_params != nil {
		fields = append(fields, nodedefinition.FieldOutputParams)
	}
	if m.input_ports != nil {
		fields = append(fields, nodedefinition.FieldInputPorts)
	}
	if m.output_ports != nil {
		fields = append(fields, nodedefinition.FieldOutputPorts)
	}
	if m.exception != nil {
		fields = append(fields, nodedefinition.FieldException)
	}
	if m._path != nil {
		fields = append(fields, nodedefinition.FieldPath)
	}
	if m.builtin != nil {
		fields = append(fields, nodedefinition.FieldBuiltin)
	}
	if m.enabled != nil {
		fields = append(fields, nodedefinition.FieldEnabled)
	}
	if m.created_at != nil {
		fields = append(fields, nodedefinition.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, nodedefinition.FieldUpdatedAt)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *NodeDefinitionMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case nodedefinition.FieldPluginName:
		return m.PluginName()
	case nodedefinition.FieldName:
		return m.Name()
	case nodedefinition.FieldAuthor:
		return m.Author()
	case nodedefinition.FieldDescription:
		return m.Description()
	case nodedefinition.FieldIcon:
		return m.Icon()
	case nodedefinition.FieldType:
		return m.GetType()
	case nodedefinition.FieldVersion:
		return m.Version()
	case nodedefinition.FieldCategory:
		return m.Category()
	case nodedefinition.FieldInputParams:
		return m.InputParams()
	case nodedefinition.FieldOutputParams:
		return m.OutputParams()
	case nodedefinition.FieldInputPorts:
		return m.InputPorts()
	case nodedefinition.FieldOutputPorts:
		return m.OutputPorts()
	case nodedefinition.FieldException:
		return m.Exception()
	case nodedefinition.FieldPath:
		return m.Path()
	case nodedefinition.FieldBuiltin:
		return m.Builtin()
	case nodedefinition.FieldEnabled:
		return m.Enabled()
	case nodedefinition.FieldCreatedAt:
		return m.CreatedAt()
	case nodedefinition.FieldUpdatedAt:
		return m.UpdatedAt()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *NodeDefinitionMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case nodedefinition.FieldPluginName:
		return m.OldPluginName(ctx)
	case nodedefinition.FieldName:
		return m.OldName(ctx)
	case nodedefinition.FieldAuthor:
		return m.OldAuthor(ctx)
	case nodedefinition.FieldDescription:
		return m.OldDescription(ctx)
	case nodedefinition.FieldIcon:
		return m.OldIcon(ctx)
	case nodedefinition.FieldType:
		return m.OldType(ctx)
	case nodedefinition.FieldVersion:
		return m.OldVersion(ctx)
	case nodedefinition.FieldCategory:
		return m.OldCategory(ctx)
	case nodedefinition.FieldInputParams:
		return m.OldInputParams(ctx)
	case nodedefinition.FieldOutputParams:
		return m.OldOutputParams(ctx)
	case nodedefinition.FieldInputPorts:
		return m.OldInputPorts(ctx)
	case nodedefinition.FieldOutputPorts:
		return m.OldOutputPorts(ctx)
	case nodedefinition.FieldException:
		return m.OldException(ctx)
	case nodedefinition.FieldPath:
		return m.OldPath(ctx)
	case nodedefinition.FieldBuiltin:
		return m.OldBuiltin(ctx)
	case nodedefinition.FieldEnabled:
		return m.OldEnabled(ctx)
	case nodedefinition.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case nodedefinition.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	}
	return nil, fmt.Errorf("unknown NodeDefinition field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *NodeDefinitionMutation) SetField(name string, value ent.Value) error {
	switch name {
	case nodedefinition.FieldPluginName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPluginName(v)
		return nil
	case nodedefinition.FieldName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetName(v)
		return nil
	case nodedefinition.FieldAuthor:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetAuthor(v)
		return nil
	case nodedefinition.FieldDescription:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDescription(v)
		return nil
	case nodedefinition.FieldIcon:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIcon(v)
		return nil
	case nodedefinition.FieldType:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetType(v)
		return nil
	case nodedefinition.FieldVersion:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetVersion(v)
		return nil
	case nodedefinition.FieldCategory:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCategory(v)
		return nil
	case nodedefinition.FieldInputParams:
		v, ok := value.([]*domain.NodeParam)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetInputParams(v)
		return nil
	case nodedefinition.FieldOutputParams:
		v, ok := value.([]*domain.NodeParam)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetOutputParams(v)
		return nil
	case nodedefinition.FieldInputPorts:
		v, ok := value.([]*domain.NodePort)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetInputPorts(v)
		return nil
	case nodedefinition.FieldOutputPorts:
		v, ok := value.([]*domain.NodePort)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetOutputPorts(v)
		return nil
	case nodedefinition.FieldException:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetException(v)
		return nil
	case nodedefinition.FieldPath:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPath(v)
		return nil
	case nodedefinition.FieldBuiltin:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetBuiltin(v)
		return nil
	case nodedefinition.FieldEnabled:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetEnabled(v)
		return nil
	case nodedefinition.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case nodedefinition.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	}
	return fmt.Errorf("unknown NodeDefinition field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *NodeDefinitionMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *NodeDefinitionMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *NodeDefinitionMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown NodeDefinition numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *NodeDefinitionMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *NodeDefinitionMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *NodeDefinitionMutation) ClearField(name string) error {
	return fmt.Errorf("unknown NodeDefinition nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *NodeDefinitionMutation) ResetField(name string) error {
	switch name {
	case nodedefinition.FieldPluginName:
		m.ResetPluginName()
		return nil
	case nodedefinition.FieldName:
		m.ResetName()
		return nil
	case nodedefinition.FieldAuthor:
		m.ResetAuthor()
		return nil
	case nodedefinition.FieldDescription:
		m.ResetDescription()
		return nil
	case nodedefinition.FieldIcon:
		m.ResetIcon()
		return nil
	case nodedefinition.FieldType:
		m.ResetType()
		return nil
	case nodedefinition.FieldVersion:
		m.ResetVersion()
		return nil
	case nodedefinition.FieldCategory:
		m.ResetCategory()
		return nil
	case nodedefinition.FieldInputParams:
		m.ResetInputParams()
		return nil
	case nodedefinition.FieldOutputParams:
		m.ResetOutputParams()
		return nil
	case nodedefinition.FieldInputPorts:
		m.ResetInputPorts()
		return nil
	case nodedefinition.FieldOutputPorts:
		m.ResetOutputPorts()
		return nil
	case nodedefinition.FieldException:
		m.ResetException()
		return nil
	case nodedefinition.FieldPath:
		m.ResetPath()
		return nil
	case nodedefinition.FieldBuiltin:
		m.ResetBuiltin()
		return nil
	case nodedefinition.FieldEnabled:
		m.ResetEnabled()
		return nil
	case nodedefinition.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case nodedefinition.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	}
	return fmt.Errorf("unknown NodeDefinition field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *NodeDefinitionMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *NodeDefinitionMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *NodeDefinitionMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *NodeDefinitionMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *NodeDefinitionMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *NodeDefinitionMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *NodeDefinitionMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown NodeDefinition unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *NodeDefinitionMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown NodeDefinition edge %s", name)
}

// PluginMutation represents an operation that mutates the Plugin nodes in the graph.
type PluginMutation struct {
	config
	op            Op
	typ           string
	id            *uuid.UUID
	name          *string
	version       *string
	author        *string
	display_name  *string
	description   *string
	icon          *string
	_path         *string
	builtin       *bool
	enabled       *bool
	created_at    *time.Time
	updated_at    *time.Time
	clearedFields map[string]struct{}
	done          bool
	oldValue      func(context.Context) (*Plugin, error)
	predicates    []predicate.Plugin
}

var _ ent.Mutation = (*PluginMutation)(nil)

// pluginOption allows management of the mutation configuration using functional options.
type pluginOption func(*PluginMutation)

// newPluginMutation creates new mutation for the Plugin entity.
func newPluginMutation(c config, op Op, opts ...pluginOption) *PluginMutation {
	m := &PluginMutation{
		config:        c,
		op:            op,
		typ:           TypePlugin,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withPluginID sets the ID field of the mutation.
func withPluginID(id uuid.UUID) pluginOption {
	return func(m *PluginMutation) {
		var (
			err   error
			once  sync.Once
			value *Plugin
		)
		m.oldValue = func(ctx context.Context) (*Plugin, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Plugin.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withPlugin sets the old Plugin of the mutation.
func withPlugin(node *Plugin) pluginOption {
	return func(m *PluginMutation) {
		m.oldValue = func(context.Context) (*Plugin, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m PluginMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m PluginMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Plugin entities.
func (m *PluginMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *PluginMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *PluginMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Plugin.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetName sets the "name" field.
func (m *PluginMutation) SetName(s string) {
	m.name = &s
}

// Name returns the value of the "name" field in the mutation.
func (m *PluginMutation) Name() (r string, exists bool) {
	v := m.name
	if v == nil {
		return
	}
	return *v, true
}

// OldName returns the old "name" field's value of the Plugin entity.
// If the Plugin object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PluginMutation) OldName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldName: %w", err)
	}
	return oldValue.Name, nil
}

// ResetName resets all changes to the "name" field.
func (m *PluginMutation) ResetName() {
	m.name = nil
}

// SetVersion sets the "version" field.
func (m *PluginMutation) SetVersion(s string) {
	m.version = &s
}

// Version returns the value of the "version" field in the mutation.
func (m *PluginMutation) Version() (r string, exists bool) {
	v := m.version
	if v == nil {
		return
	}
	return *v, true
}

// OldVersion returns the old "version" field's value of the Plugin entity.
// If the Plugin object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PluginMutation) OldVersion(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldVersion is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldVersion requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldVersion: %w", err)
	}
	return oldValue.Version, nil
}

// ResetVersion resets all changes to the "version" field.
func (m *PluginMutation) ResetVersion() {
	m.version = nil
}

// SetAuthor sets the "author" field.
func (m *PluginMutation) SetAuthor(s string) {
	m.author = &s
}

// Author returns the value of the "author" field in the mutation.
func (m *PluginMutation) Author() (r string, exists bool) {
	v := m.author
	if v == nil {
		return
	}
	return *v, true
}

// OldAuthor returns the old "author" field's value of the Plugin entity.
// If the Plugin object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PluginMutation) OldAuthor(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldAuthor is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldAuthor requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldAuthor: %w", err)
	}
	return oldValue.Author, nil
}

// ResetAuthor resets all changes to the "author" field.
func (m *PluginMutation) ResetAuthor() {
	m.author = nil
}

// SetDisplayName sets the "display_name" field.
func (m *PluginMutation) SetDisplayName(s string) {
	m.display_name = &s
}

// DisplayName returns the value of the "display_name" field in the mutation.
func (m *PluginMutation) DisplayName() (r string, exists bool) {
	v := m.display_name
	if v == nil {
		return
	}
	return *v, true
}

// OldDisplayName returns the old "display_name" field's value of the Plugin entity.
// If the Plugin object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PluginMutation) OldDisplayName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDisplayName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDisplayName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDisplayName: %w", err)
	}
	return oldValue.DisplayName, nil
}

// ResetDisplayName resets all changes to the "display_name" field.
func (m *PluginMutation) ResetDisplayName() {
	m.display_name = nil
}

// SetDescription sets the "description" field.
func (m *PluginMutation) SetDescription(s string) {
	m.description = &s
}

// Description returns the value of the "description" field in the mutation.
func (m *PluginMutation) Description() (r string, exists bool) {
	v := m.description
	if v == nil {
		return
	}
	return *v, true
}

// OldDescription returns the old "description" field's value of the Plugin entity.
// If the Plugin object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PluginMutation) OldDescription(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDescription is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDescription requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDescription: %w", err)
	}
	return oldValue.Description, nil
}

// ResetDescription resets all changes to the "description" field.
func (m *PluginMutation) ResetDescription() {
	m.description = nil
}

// SetIcon sets the "icon" field.
func (m *PluginMutation) SetIcon(s string) {
	m.icon = &s
}

// Icon returns the value of the "icon" field in the mutation.
func (m *PluginMutation) Icon() (r string, exists bool) {
	v := m.icon
	if v == nil {
		return
	}
	return *v, true
}

// OldIcon returns the old "icon" field's value of the Plugin entity.
// If the Plugin object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PluginMutation) OldIcon(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIcon is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIcon requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIcon: %w", err)
	}
	return oldValue.Icon, nil
}

// ResetIcon resets all changes to the "icon" field.
func (m *PluginMutation) ResetIcon() {
	m.icon = nil
}

// SetPath sets the "path" field.
func (m *PluginMutation) SetPath(s string) {
	m._path = &s
}

// Path returns the value of the "path" field in the mutation.
func (m *PluginMutation) Path() (r string, exists bool) {
	v := m._path
	if v == nil {
		return
	}
	return *v, true
}

// OldPath returns the old "path" field's value of the Plugin entity.
// If the Plugin object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PluginMutation) OldPath(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPath is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPath requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPath: %w", err)
	}
	return oldValue.Path, nil
}

// ResetPath resets all changes to the "path" field.
func (m *PluginMutation) ResetPath() {
	m._path = nil
}

// SetBuiltin sets the "builtin" field.
func (m *PluginMutation) SetBuiltin(b bool) {
	m.builtin = &b
}

// Builtin returns the value of the "builtin" field in the mutation.
func (m *PluginMutation) Builtin() (r bool, exists bool) {
	v := m.builtin
	if v == nil {
		return
	}
	return *v, true
}

// OldBuiltin returns the old "builtin" field's value of the Plugin entity.
// If the Plugin object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PluginMutation) OldBuiltin(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldBuiltin is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldBuiltin requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldBuiltin: %w", err)
	}
	return oldValue.Builtin, nil
}

// ResetBuiltin resets all changes to the "builtin" field.
func (m *PluginMutation) ResetBuiltin() {
	m.builtin = nil
}

// SetEnabled sets the "enabled" field.
func (m *PluginMutation) SetEnabled(b bool) {
	m.enabled = &b
}

// Enabled returns the value of the "enabled" field in the mutation.
func (m *PluginMutation) Enabled() (r bool, exists bool) {
	v := m.enabled
	if v == nil {
		return
	}
	return *v, true
}

// OldEnabled returns the old "enabled" field's value of the Plugin entity.
// If the Plugin object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PluginMutation) OldEnabled(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldEnabled is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldEnabled requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldEnabled: %w", err)
	}
	return oldValue.Enabled, nil
}

// ResetEnabled resets all changes to the "enabled" field.
func (m *PluginMutation) ResetEnabled() {
	m.enabled = nil
}

// SetCreatedAt sets the "created_at" field.
func (m *PluginMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *PluginMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the Plugin entity.
// If the Plugin object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PluginMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *PluginMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *PluginMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *PluginMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the Plugin entity.
// If the Plugin object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PluginMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *PluginMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// Where appends a list predicates to the PluginMutation builder.
func (m *PluginMutation) Where(ps ...predicate.Plugin) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the PluginMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *PluginMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Plugin, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *PluginMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *PluginMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Plugin).
func (m *PluginMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *PluginMutation) Fields() []string {
	fields := make([]string, 0, 11)
	if m.name != nil {
		fields = append(fields, plugin.FieldName)
	}
	if m.version != nil {
		fields = append(fields, plugin.FieldVersion)
	}
	if m.author != nil {
		fields = append(fields, plugin.FieldAuthor)
	}
	if m.display_name != nil {
		fields = append(fields, plugin.FieldDisplayName)
	}
	if m.description != nil {
		fields = append(fields, plugin.FieldDescription)
	}
	if m.icon != nil {
		fields = append(fields, plugin.FieldIcon)
	}
	if m._path != nil {
		fields = append(fields, plugin.FieldPath)
	}
	if m.builtin != nil {
		fields = append(fields, plugin.FieldBuiltin)
	}
	if m.enabled != nil {
		fields = append(fields, plugin.FieldEnabled)
	}
	if m.created_at != nil {
		fields = append(fields, plugin.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, plugin.FieldUpdatedAt)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *PluginMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case plugin.FieldName:
		return m.Name()
	case plugin.FieldVersion:
		return m.Version()
	case plugin.FieldAuthor:
		return m.Author()
	case plugin.FieldDisplayName:
		return m.DisplayName()
	case plugin.FieldDescription:
		return m.Description()
	case plugin.FieldIcon:
		return m.Icon()
	case plugin.FieldPath:
		return m.Path()
	case plugin.FieldBuiltin:
		return m.Builtin()
	case plugin.FieldEnabled:
		return m.Enabled()
	case plugin.FieldCreatedAt:
		return m.CreatedAt()
	case plugin.FieldUpdatedAt:
		return m.UpdatedAt()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *PluginMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case plugin.FieldName:
		return m.OldName(ctx)
	case plugin.FieldVersion:
		return m.OldVersion(ctx)
	case plugin.FieldAuthor:
		return m.OldAuthor(ctx)
	case plugin.FieldDisplayName:
		return m.OldDisplayName(ctx)
	case plugin.FieldDescription:
		return m.OldDescription(ctx)
	case plugin.FieldIcon:
		return m.OldIcon(ctx)
	case plugin.FieldPath:
		return m.OldPath(ctx)
	case plugin.FieldBuiltin:
		return m.OldBuiltin(ctx)
	case plugin.FieldEnabled:
		return m.OldEnabled(ctx)
	case plugin.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case plugin.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	}
	return nil, fmt.Errorf("unknown Plugin field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *PluginMutation) SetField(name string, value ent.Value) error {
	switch name {
	case plugin.FieldName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetName(v)
		return nil
	case plugin.FieldVersion:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetVersion(v)
		return nil
	case plugin.FieldAuthor:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetAuthor(v)
		return nil
	case plugin.FieldDisplayName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDisplayName(v)
		return nil
	case plugin.FieldDescription:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDescription(v)
		return nil
	case plugin.FieldIcon:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIcon(v)
		return nil
	case plugin.FieldPath:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPath(v)
		return nil
	case plugin.FieldBuiltin:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetBuiltin(v)
		return nil
	case plugin.FieldEnabled:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetEnabled(v)
		return nil
	case plugin.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case plugin.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	}
	return fmt.Errorf("unknown Plugin field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *PluginMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *PluginMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *PluginMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown Plugin numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *PluginMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *PluginMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *PluginMutation) ClearField(name string) error {
	return fmt.Errorf("unknown Plugin nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *PluginMutation) ResetField(name string) error {
	switch name {
	case plugin.FieldName:
		m.ResetName()
		return nil
	case plugin.FieldVersion:
		m.ResetVersion()
		return nil
	case plugin.FieldAuthor:
		m.ResetAuthor()
		return nil
	case plugin.FieldDisplayName:
		m.ResetDisplayName()
		return nil
	case plugin.FieldDescription:
		m.ResetDescription()
		return nil
	case plugin.FieldIcon:
		m.ResetIcon()
		return nil
	case plugin.FieldPath:
		m.ResetPath()
		return nil
	case plugin.FieldBuiltin:
		m.ResetBuiltin()
		return nil
	case plugin.FieldEnabled:
		m.ResetEnabled()
		return nil
	case plugin.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case plugin.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	}
	return fmt.Errorf("unknown Plugin field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *PluginMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *PluginMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *PluginMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *PluginMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *PluginMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *PluginMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *PluginMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown Plugin unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *PluginMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown Plugin edge %s", name)
}

// UserMutation represents an operation that mutates the User nodes in the graph.
type UserMutation struct {
	config
	op               Op
	typ              string
	id               *uuid.UUID
	username         *string
	password         *string
	nickname         *string
	status           *enums.Status
	created_at       *time.Time
	updated_at       *time.Time
	clearedFields    map[string]struct{}
	workflows        map[uuid.UUID]struct{}
	removedworkflows map[uuid.UUID]struct{}
	clearedworkflows bool
	done             bool
	oldValue         func(context.Context) (*User, error)
	predicates       []predicate.User
}

var _ ent.Mutation = (*UserMutation)(nil)

// userOption allows management of the mutation configuration using functional options.
type userOption func(*UserMutation)

// newUserMutation creates new mutation for the User entity.
func newUserMutation(c config, op Op, opts ...userOption) *UserMutation {
	m := &UserMutation{
		config:        c,
		op:            op,
		typ:           TypeUser,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withUserID sets the ID field of the mutation.
func withUserID(id uuid.UUID) userOption {
	return func(m *UserMutation) {
		var (
			err   error
			once  sync.Once
			value *User
		)
		m.oldValue = func(ctx context.Context) (*User, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().User.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withUser sets the old User of the mutation.
func withUser(node *User) userOption {
	return func(m *UserMutation) {
		m.oldValue = func(context.Context) (*User, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m UserMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m UserMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of User entities.
func (m *UserMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *UserMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *UserMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().User.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetUsername sets the "username" field.
func (m *UserMutation) SetUsername(s string) {
	m.username = &s
}

// Username returns the value of the "username" field in the mutation.
func (m *UserMutation) Username() (r string, exists bool) {
	v := m.username
	if v == nil {
		return
	}
	return *v, true
}

// OldUsername returns the old "username" field's value of the User entity.
// If the User object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserMutation) OldUsername(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUsername is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUsername requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUsername: %w", err)
	}
	return oldValue.Username, nil
}

// ResetUsername resets all changes to the "username" field.
func (m *UserMutation) ResetUsername() {
	m.username = nil
}

// SetPassword sets the "password" field.
func (m *UserMutation) SetPassword(s string) {
	m.password = &s
}

// Password returns the value of the "password" field in the mutation.
func (m *UserMutation) Password() (r string, exists bool) {
	v := m.password
	if v == nil {
		return
	}
	return *v, true
}

// OldPassword returns the old "password" field's value of the User entity.
// If the User object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserMutation) OldPassword(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPassword is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPassword requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPassword: %w", err)
	}
	return oldValue.Password, nil
}

// ResetPassword resets all changes to the "password" field.
func (m *UserMutation) ResetPassword() {
	m.password = nil
}

// SetNickname sets the "nickname" field.
func (m *UserMutation) SetNickname(s string) {
	m.nickname = &s
}

// Nickname returns the value of the "nickname" field in the mutation.
func (m *UserMutation) Nickname() (r string, exists bool) {
	v := m.nickname
	if v == nil {
		return
	}
	return *v, true
}

// OldNickname returns the old "nickname" field's value of the User entity.
// If the User object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserMutation) OldNickname(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldNickname is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldNickname requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldNickname: %w", err)
	}
	return oldValue.Nickname, nil
}

// ClearNickname clears the value of the "nickname" field.
func (m *UserMutation) ClearNickname() {
	m.nickname = nil
	m.clearedFields[user.FieldNickname] = struct{}{}
}

// NicknameCleared returns if the "nickname" field was cleared in this mutation.
func (m *UserMutation) NicknameCleared() bool {
	_, ok := m.clearedFields[user.FieldNickname]
	return ok
}

// ResetNickname resets all changes to the "nickname" field.
func (m *UserMutation) ResetNickname() {
	m.nickname = nil
	delete(m.clearedFields, user.FieldNickname)
}

// SetStatus sets the "status" field.
func (m *UserMutation) SetStatus(e enums.Status) {
	m.status = &e
}

// Status returns the value of the "status" field in the mutation.
func (m *UserMutation) Status() (r enums.Status, exists bool) {
	v := m.status
	if v == nil {
		return
	}
	return *v, true
}

// OldStatus returns the old "status" field's value of the User entity.
// If the User object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserMutation) OldStatus(ctx context.Context) (v enums.Status, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldStatus is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldStatus requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldStatus: %w", err)
	}
	return oldValue.Status, nil
}

// ResetStatus resets all changes to the "status" field.
func (m *UserMutation) ResetStatus() {
	m.status = nil
}

// SetCreatedAt sets the "created_at" field.
func (m *UserMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *UserMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the User entity.
// If the User object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *UserMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *UserMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *UserMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the User entity.
// If the User object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *UserMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// AddWorkflowIDs adds the "workflows" edge to the Workflow entity by ids.
func (m *UserMutation) AddWorkflowIDs(ids ...uuid.UUID) {
	if m.workflows == nil {
		m.workflows = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		m.workflows[ids[i]] = struct{}{}
	}
}

// ClearWorkflows clears the "workflows" edge to the Workflow entity.
func (m *UserMutation) ClearWorkflows() {
	m.clearedworkflows = true
}

// WorkflowsCleared reports if the "workflows" edge to the Workflow entity was cleared.
func (m *UserMutation) WorkflowsCleared() bool {
	return m.clearedworkflows
}

// RemoveWorkflowIDs removes the "workflows" edge to the Workflow entity by IDs.
func (m *UserMutation) RemoveWorkflowIDs(ids ...uuid.UUID) {
	if m.removedworkflows == nil {
		m.removedworkflows = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		delete(m.workflows, ids[i])
		m.removedworkflows[ids[i]] = struct{}{}
	}
}

// RemovedWorkflows returns the removed IDs of the "workflows" edge to the Workflow entity.
func (m *UserMutation) RemovedWorkflowsIDs() (ids []uuid.UUID) {
	for id := range m.removedworkflows {
		ids = append(ids, id)
	}
	return
}

// WorkflowsIDs returns the "workflows" edge IDs in the mutation.
func (m *UserMutation) WorkflowsIDs() (ids []uuid.UUID) {
	for id := range m.workflows {
		ids = append(ids, id)
	}
	return
}

// ResetWorkflows resets all changes to the "workflows" edge.
func (m *UserMutation) ResetWorkflows() {
	m.workflows = nil
	m.clearedworkflows = false
	m.removedworkflows = nil
}

// Where appends a list predicates to the UserMutation builder.
func (m *UserMutation) Where(ps ...predicate.User) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the UserMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *UserMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.User, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *UserMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *UserMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (User).
func (m *UserMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *UserMutation) Fields() []string {
	fields := make([]string, 0, 6)
	if m.username != nil {
		fields = append(fields, user.FieldUsername)
	}
	if m.password != nil {
		fields = append(fields, user.FieldPassword)
	}
	if m.nickname != nil {
		fields = append(fields, user.FieldNickname)
	}
	if m.status != nil {
		fields = append(fields, user.FieldStatus)
	}
	if m.created_at != nil {
		fields = append(fields, user.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, user.FieldUpdatedAt)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *UserMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case user.FieldUsername:
		return m.Username()
	case user.FieldPassword:
		return m.Password()
	case user.FieldNickname:
		return m.Nickname()
	case user.FieldStatus:
		return m.Status()
	case user.FieldCreatedAt:
		return m.CreatedAt()
	case user.FieldUpdatedAt:
		return m.UpdatedAt()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *UserMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case user.FieldUsername:
		return m.OldUsername(ctx)
	case user.FieldPassword:
		return m.OldPassword(ctx)
	case user.FieldNickname:
		return m.OldNickname(ctx)
	case user.FieldStatus:
		return m.OldStatus(ctx)
	case user.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case user.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	}
	return nil, fmt.Errorf("unknown User field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *UserMutation) SetField(name string, value ent.Value) error {
	switch name {
	case user.FieldUsername:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUsername(v)
		return nil
	case user.FieldPassword:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPassword(v)
		return nil
	case user.FieldNickname:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetNickname(v)
		return nil
	case user.FieldStatus:
		v, ok := value.(enums.Status)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetStatus(v)
		return nil
	case user.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case user.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	}
	return fmt.Errorf("unknown User field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *UserMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *UserMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *UserMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown User numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *UserMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(user.FieldNickname) {
		fields = append(fields, user.FieldNickname)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *UserMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *UserMutation) ClearField(name string) error {
	switch name {
	case user.FieldNickname:
		m.ClearNickname()
		return nil
	}
	return fmt.Errorf("unknown User nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *UserMutation) ResetField(name string) error {
	switch name {
	case user.FieldUsername:
		m.ResetUsername()
		return nil
	case user.FieldPassword:
		m.ResetPassword()
		return nil
	case user.FieldNickname:
		m.ResetNickname()
		return nil
	case user.FieldStatus:
		m.ResetStatus()
		return nil
	case user.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case user.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	}
	return fmt.Errorf("unknown User field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *UserMutation) AddedEdges() []string {
	edges := make([]string, 0, 1)
	if m.workflows != nil {
		edges = append(edges, user.EdgeWorkflows)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *UserMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case user.EdgeWorkflows:
		ids := make([]ent.Value, 0, len(m.workflows))
		for id := range m.workflows {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *UserMutation) RemovedEdges() []string {
	edges := make([]string, 0, 1)
	if m.removedworkflows != nil {
		edges = append(edges, user.EdgeWorkflows)
	}
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *UserMutation) RemovedIDs(name string) []ent.Value {
	switch name {
	case user.EdgeWorkflows:
		ids := make([]ent.Value, 0, len(m.removedworkflows))
		for id := range m.removedworkflows {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *UserMutation) ClearedEdges() []string {
	edges := make([]string, 0, 1)
	if m.clearedworkflows {
		edges = append(edges, user.EdgeWorkflows)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *UserMutation) EdgeCleared(name string) bool {
	switch name {
	case user.EdgeWorkflows:
		return m.clearedworkflows
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *UserMutation) ClearEdge(name string) error {
	switch name {
	}
	return fmt.Errorf("unknown User unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *UserMutation) ResetEdge(name string) error {
	switch name {
	case user.EdgeWorkflows:
		m.ResetWorkflows()
		return nil
	}
	return fmt.Errorf("unknown User edge %s", name)
}

// WorkflowMutation represents an operation that mutates the Workflow nodes in the graph.
type WorkflowMutation struct {
	config
	op            Op
	typ           string
	id            *uuid.UUID
	name          *string
	icon          *string
	description   *string
	status        *enums.Status
	viewport      **v1.WorkflowViewport
	created_at    *time.Time
	updated_at    *time.Time
	clearedFields map[string]struct{}
	user          *uuid.UUID
	cleareduser   bool
	nodes         map[uuid.UUID]struct{}
	removednodes  map[uuid.UUID]struct{}
	clearednodes  bool
	links         map[uuid.UUID]struct{}
	removedlinks  map[uuid.UUID]struct{}
	clearedlinks  bool
	done          bool
	oldValue      func(context.Context) (*Workflow, error)
	predicates    []predicate.Workflow
}

var _ ent.Mutation = (*WorkflowMutation)(nil)

// workflowOption allows management of the mutation configuration using functional options.
type workflowOption func(*WorkflowMutation)

// newWorkflowMutation creates new mutation for the Workflow entity.
func newWorkflowMutation(c config, op Op, opts ...workflowOption) *WorkflowMutation {
	m := &WorkflowMutation{
		config:        c,
		op:            op,
		typ:           TypeWorkflow,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withWorkflowID sets the ID field of the mutation.
func withWorkflowID(id uuid.UUID) workflowOption {
	return func(m *WorkflowMutation) {
		var (
			err   error
			once  sync.Once
			value *Workflow
		)
		m.oldValue = func(ctx context.Context) (*Workflow, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Workflow.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withWorkflow sets the old Workflow of the mutation.
func withWorkflow(node *Workflow) workflowOption {
	return func(m *WorkflowMutation) {
		m.oldValue = func(context.Context) (*Workflow, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m WorkflowMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m WorkflowMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Workflow entities.
func (m *WorkflowMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *WorkflowMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *WorkflowMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Workflow.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetUserID sets the "user_id" field.
func (m *WorkflowMutation) SetUserID(u uuid.UUID) {
	m.user = &u
}

// UserID returns the value of the "user_id" field in the mutation.
func (m *WorkflowMutation) UserID() (r uuid.UUID, exists bool) {
	v := m.user
	if v == nil {
		return
	}
	return *v, true
}

// OldUserID returns the old "user_id" field's value of the Workflow entity.
// If the Workflow object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowMutation) OldUserID(ctx context.Context) (v uuid.UUID, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUserID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUserID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUserID: %w", err)
	}
	return oldValue.UserID, nil
}

// ClearUserID clears the value of the "user_id" field.
func (m *WorkflowMutation) ClearUserID() {
	m.user = nil
	m.clearedFields[workflow.FieldUserID] = struct{}{}
}

// UserIDCleared returns if the "user_id" field was cleared in this mutation.
func (m *WorkflowMutation) UserIDCleared() bool {
	_, ok := m.clearedFields[workflow.FieldUserID]
	return ok
}

// ResetUserID resets all changes to the "user_id" field.
func (m *WorkflowMutation) ResetUserID() {
	m.user = nil
	delete(m.clearedFields, workflow.FieldUserID)
}

// SetName sets the "name" field.
func (m *WorkflowMutation) SetName(s string) {
	m.name = &s
}

// Name returns the value of the "name" field in the mutation.
func (m *WorkflowMutation) Name() (r string, exists bool) {
	v := m.name
	if v == nil {
		return
	}
	return *v, true
}

// OldName returns the old "name" field's value of the Workflow entity.
// If the Workflow object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowMutation) OldName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldName: %w", err)
	}
	return oldValue.Name, nil
}

// ResetName resets all changes to the "name" field.
func (m *WorkflowMutation) ResetName() {
	m.name = nil
}

// SetIcon sets the "icon" field.
func (m *WorkflowMutation) SetIcon(s string) {
	m.icon = &s
}

// Icon returns the value of the "icon" field in the mutation.
func (m *WorkflowMutation) Icon() (r string, exists bool) {
	v := m.icon
	if v == nil {
		return
	}
	return *v, true
}

// OldIcon returns the old "icon" field's value of the Workflow entity.
// If the Workflow object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowMutation) OldIcon(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIcon is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIcon requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIcon: %w", err)
	}
	return oldValue.Icon, nil
}

// ResetIcon resets all changes to the "icon" field.
func (m *WorkflowMutation) ResetIcon() {
	m.icon = nil
}

// SetDescription sets the "description" field.
func (m *WorkflowMutation) SetDescription(s string) {
	m.description = &s
}

// Description returns the value of the "description" field in the mutation.
func (m *WorkflowMutation) Description() (r string, exists bool) {
	v := m.description
	if v == nil {
		return
	}
	return *v, true
}

// OldDescription returns the old "description" field's value of the Workflow entity.
// If the Workflow object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowMutation) OldDescription(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDescription is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDescription requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDescription: %w", err)
	}
	return oldValue.Description, nil
}

// ResetDescription resets all changes to the "description" field.
func (m *WorkflowMutation) ResetDescription() {
	m.description = nil
}

// SetStatus sets the "status" field.
func (m *WorkflowMutation) SetStatus(e enums.Status) {
	m.status = &e
}

// Status returns the value of the "status" field in the mutation.
func (m *WorkflowMutation) Status() (r enums.Status, exists bool) {
	v := m.status
	if v == nil {
		return
	}
	return *v, true
}

// OldStatus returns the old "status" field's value of the Workflow entity.
// If the Workflow object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowMutation) OldStatus(ctx context.Context) (v enums.Status, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldStatus is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldStatus requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldStatus: %w", err)
	}
	return oldValue.Status, nil
}

// ResetStatus resets all changes to the "status" field.
func (m *WorkflowMutation) ResetStatus() {
	m.status = nil
}

// SetViewport sets the "viewport" field.
func (m *WorkflowMutation) SetViewport(vv *v1.WorkflowViewport) {
	m.viewport = &vv
}

// Viewport returns the value of the "viewport" field in the mutation.
func (m *WorkflowMutation) Viewport() (r *v1.WorkflowViewport, exists bool) {
	v := m.viewport
	if v == nil {
		return
	}
	return *v, true
}

// OldViewport returns the old "viewport" field's value of the Workflow entity.
// If the Workflow object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowMutation) OldViewport(ctx context.Context) (v *v1.WorkflowViewport, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldViewport is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldViewport requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldViewport: %w", err)
	}
	return oldValue.Viewport, nil
}

// ResetViewport resets all changes to the "viewport" field.
func (m *WorkflowMutation) ResetViewport() {
	m.viewport = nil
}

// SetCreatedAt sets the "created_at" field.
func (m *WorkflowMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *WorkflowMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the Workflow entity.
// If the Workflow object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *WorkflowMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *WorkflowMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *WorkflowMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the Workflow entity.
// If the Workflow object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *WorkflowMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// ClearUser clears the "user" edge to the User entity.
func (m *WorkflowMutation) ClearUser() {
	m.cleareduser = true
	m.clearedFields[workflow.FieldUserID] = struct{}{}
}

// UserCleared reports if the "user" edge to the User entity was cleared.
func (m *WorkflowMutation) UserCleared() bool {
	return m.UserIDCleared() || m.cleareduser
}

// UserIDs returns the "user" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// UserID instead. It exists only for internal usage by the builders.
func (m *WorkflowMutation) UserIDs() (ids []uuid.UUID) {
	if id := m.user; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetUser resets all changes to the "user" edge.
func (m *WorkflowMutation) ResetUser() {
	m.user = nil
	m.cleareduser = false
}

// AddNodeIDs adds the "nodes" edge to the WorkflowNode entity by ids.
func (m *WorkflowMutation) AddNodeIDs(ids ...uuid.UUID) {
	if m.nodes == nil {
		m.nodes = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		m.nodes[ids[i]] = struct{}{}
	}
}

// ClearNodes clears the "nodes" edge to the WorkflowNode entity.
func (m *WorkflowMutation) ClearNodes() {
	m.clearednodes = true
}

// NodesCleared reports if the "nodes" edge to the WorkflowNode entity was cleared.
func (m *WorkflowMutation) NodesCleared() bool {
	return m.clearednodes
}

// RemoveNodeIDs removes the "nodes" edge to the WorkflowNode entity by IDs.
func (m *WorkflowMutation) RemoveNodeIDs(ids ...uuid.UUID) {
	if m.removednodes == nil {
		m.removednodes = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		delete(m.nodes, ids[i])
		m.removednodes[ids[i]] = struct{}{}
	}
}

// RemovedNodes returns the removed IDs of the "nodes" edge to the WorkflowNode entity.
func (m *WorkflowMutation) RemovedNodesIDs() (ids []uuid.UUID) {
	for id := range m.removednodes {
		ids = append(ids, id)
	}
	return
}

// NodesIDs returns the "nodes" edge IDs in the mutation.
func (m *WorkflowMutation) NodesIDs() (ids []uuid.UUID) {
	for id := range m.nodes {
		ids = append(ids, id)
	}
	return
}

// ResetNodes resets all changes to the "nodes" edge.
func (m *WorkflowMutation) ResetNodes() {
	m.nodes = nil
	m.clearednodes = false
	m.removednodes = nil
}

// AddLinkIDs adds the "links" edge to the WorkflowLink entity by ids.
func (m *WorkflowMutation) AddLinkIDs(ids ...uuid.UUID) {
	if m.links == nil {
		m.links = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		m.links[ids[i]] = struct{}{}
	}
}

// ClearLinks clears the "links" edge to the WorkflowLink entity.
func (m *WorkflowMutation) ClearLinks() {
	m.clearedlinks = true
}

// LinksCleared reports if the "links" edge to the WorkflowLink entity was cleared.
func (m *WorkflowMutation) LinksCleared() bool {
	return m.clearedlinks
}

// RemoveLinkIDs removes the "links" edge to the WorkflowLink entity by IDs.
func (m *WorkflowMutation) RemoveLinkIDs(ids ...uuid.UUID) {
	if m.removedlinks == nil {
		m.removedlinks = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		delete(m.links, ids[i])
		m.removedlinks[ids[i]] = struct{}{}
	}
}

// RemovedLinks returns the removed IDs of the "links" edge to the WorkflowLink entity.
func (m *WorkflowMutation) RemovedLinksIDs() (ids []uuid.UUID) {
	for id := range m.removedlinks {
		ids = append(ids, id)
	}
	return
}

// LinksIDs returns the "links" edge IDs in the mutation.
func (m *WorkflowMutation) LinksIDs() (ids []uuid.UUID) {
	for id := range m.links {
		ids = append(ids, id)
	}
	return
}

// ResetLinks resets all changes to the "links" edge.
func (m *WorkflowMutation) ResetLinks() {
	m.links = nil
	m.clearedlinks = false
	m.removedlinks = nil
}

// Where appends a list predicates to the WorkflowMutation builder.
func (m *WorkflowMutation) Where(ps ...predicate.Workflow) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the WorkflowMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *WorkflowMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Workflow, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *WorkflowMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *WorkflowMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Workflow).
func (m *WorkflowMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *WorkflowMutation) Fields() []string {
	fields := make([]string, 0, 8)
	if m.user != nil {
		fields = append(fields, workflow.FieldUserID)
	}
	if m.name != nil {
		fields = append(fields, workflow.FieldName)
	}
	if m.icon != nil {
		fields = append(fields, workflow.FieldIcon)
	}
	if m.description != nil {
		fields = append(fields, workflow.FieldDescription)
	}
	if m.status != nil {
		fields = append(fields, workflow.FieldStatus)
	}
	if m.viewport != nil {
		fields = append(fields, workflow.FieldViewport)
	}
	if m.created_at != nil {
		fields = append(fields, workflow.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, workflow.FieldUpdatedAt)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *WorkflowMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case workflow.FieldUserID:
		return m.UserID()
	case workflow.FieldName:
		return m.Name()
	case workflow.FieldIcon:
		return m.Icon()
	case workflow.FieldDescription:
		return m.Description()
	case workflow.FieldStatus:
		return m.Status()
	case workflow.FieldViewport:
		return m.Viewport()
	case workflow.FieldCreatedAt:
		return m.CreatedAt()
	case workflow.FieldUpdatedAt:
		return m.UpdatedAt()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *WorkflowMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case workflow.FieldUserID:
		return m.OldUserID(ctx)
	case workflow.FieldName:
		return m.OldName(ctx)
	case workflow.FieldIcon:
		return m.OldIcon(ctx)
	case workflow.FieldDescription:
		return m.OldDescription(ctx)
	case workflow.FieldStatus:
		return m.OldStatus(ctx)
	case workflow.FieldViewport:
		return m.OldViewport(ctx)
	case workflow.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case workflow.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	}
	return nil, fmt.Errorf("unknown Workflow field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *WorkflowMutation) SetField(name string, value ent.Value) error {
	switch name {
	case workflow.FieldUserID:
		v, ok := value.(uuid.UUID)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUserID(v)
		return nil
	case workflow.FieldName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetName(v)
		return nil
	case workflow.FieldIcon:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIcon(v)
		return nil
	case workflow.FieldDescription:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDescription(v)
		return nil
	case workflow.FieldStatus:
		v, ok := value.(enums.Status)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetStatus(v)
		return nil
	case workflow.FieldViewport:
		v, ok := value.(*v1.WorkflowViewport)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetViewport(v)
		return nil
	case workflow.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case workflow.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	}
	return fmt.Errorf("unknown Workflow field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *WorkflowMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *WorkflowMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *WorkflowMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown Workflow numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *WorkflowMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(workflow.FieldUserID) {
		fields = append(fields, workflow.FieldUserID)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *WorkflowMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *WorkflowMutation) ClearField(name string) error {
	switch name {
	case workflow.FieldUserID:
		m.ClearUserID()
		return nil
	}
	return fmt.Errorf("unknown Workflow nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *WorkflowMutation) ResetField(name string) error {
	switch name {
	case workflow.FieldUserID:
		m.ResetUserID()
		return nil
	case workflow.FieldName:
		m.ResetName()
		return nil
	case workflow.FieldIcon:
		m.ResetIcon()
		return nil
	case workflow.FieldDescription:
		m.ResetDescription()
		return nil
	case workflow.FieldStatus:
		m.ResetStatus()
		return nil
	case workflow.FieldViewport:
		m.ResetViewport()
		return nil
	case workflow.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case workflow.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	}
	return fmt.Errorf("unknown Workflow field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *WorkflowMutation) AddedEdges() []string {
	edges := make([]string, 0, 3)
	if m.user != nil {
		edges = append(edges, workflow.EdgeUser)
	}
	if m.nodes != nil {
		edges = append(edges, workflow.EdgeNodes)
	}
	if m.links != nil {
		edges = append(edges, workflow.EdgeLinks)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *WorkflowMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case workflow.EdgeUser:
		if id := m.user; id != nil {
			return []ent.Value{*id}
		}
	case workflow.EdgeNodes:
		ids := make([]ent.Value, 0, len(m.nodes))
		for id := range m.nodes {
			ids = append(ids, id)
		}
		return ids
	case workflow.EdgeLinks:
		ids := make([]ent.Value, 0, len(m.links))
		for id := range m.links {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *WorkflowMutation) RemovedEdges() []string {
	edges := make([]string, 0, 3)
	if m.removednodes != nil {
		edges = append(edges, workflow.EdgeNodes)
	}
	if m.removedlinks != nil {
		edges = append(edges, workflow.EdgeLinks)
	}
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *WorkflowMutation) RemovedIDs(name string) []ent.Value {
	switch name {
	case workflow.EdgeNodes:
		ids := make([]ent.Value, 0, len(m.removednodes))
		for id := range m.removednodes {
			ids = append(ids, id)
		}
		return ids
	case workflow.EdgeLinks:
		ids := make([]ent.Value, 0, len(m.removedlinks))
		for id := range m.removedlinks {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *WorkflowMutation) ClearedEdges() []string {
	edges := make([]string, 0, 3)
	if m.cleareduser {
		edges = append(edges, workflow.EdgeUser)
	}
	if m.clearednodes {
		edges = append(edges, workflow.EdgeNodes)
	}
	if m.clearedlinks {
		edges = append(edges, workflow.EdgeLinks)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *WorkflowMutation) EdgeCleared(name string) bool {
	switch name {
	case workflow.EdgeUser:
		return m.cleareduser
	case workflow.EdgeNodes:
		return m.clearednodes
	case workflow.EdgeLinks:
		return m.clearedlinks
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *WorkflowMutation) ClearEdge(name string) error {
	switch name {
	case workflow.EdgeUser:
		m.ClearUser()
		return nil
	}
	return fmt.Errorf("unknown Workflow unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *WorkflowMutation) ResetEdge(name string) error {
	switch name {
	case workflow.EdgeUser:
		m.ResetUser()
		return nil
	case workflow.EdgeNodes:
		m.ResetNodes()
		return nil
	case workflow.EdgeLinks:
		m.ResetLinks()
		return nil
	}
	return fmt.Errorf("unknown Workflow edge %s", name)
}

// WorkflowLinkMutation represents an operation that mutates the WorkflowLink nodes in the graph.
type WorkflowLinkMutation struct {
	config
	op              Op
	typ             string
	id              *uuid.UUID
	from_node_id    *uuid.UUID
	to_node_id      *uuid.UUID
	from_port_id    *string
	to_port_id      *string
	_type           *string
	created_at      *time.Time
	updated_at      *time.Time
	clearedFields   map[string]struct{}
	workflow        *uuid.UUID
	clearedworkflow bool
	done            bool
	oldValue        func(context.Context) (*WorkflowLink, error)
	predicates      []predicate.WorkflowLink
}

var _ ent.Mutation = (*WorkflowLinkMutation)(nil)

// workflowlinkOption allows management of the mutation configuration using functional options.
type workflowlinkOption func(*WorkflowLinkMutation)

// newWorkflowLinkMutation creates new mutation for the WorkflowLink entity.
func newWorkflowLinkMutation(c config, op Op, opts ...workflowlinkOption) *WorkflowLinkMutation {
	m := &WorkflowLinkMutation{
		config:        c,
		op:            op,
		typ:           TypeWorkflowLink,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withWorkflowLinkID sets the ID field of the mutation.
func withWorkflowLinkID(id uuid.UUID) workflowlinkOption {
	return func(m *WorkflowLinkMutation) {
		var (
			err   error
			once  sync.Once
			value *WorkflowLink
		)
		m.oldValue = func(ctx context.Context) (*WorkflowLink, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().WorkflowLink.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withWorkflowLink sets the old WorkflowLink of the mutation.
func withWorkflowLink(node *WorkflowLink) workflowlinkOption {
	return func(m *WorkflowLinkMutation) {
		m.oldValue = func(context.Context) (*WorkflowLink, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m WorkflowLinkMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m WorkflowLinkMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of WorkflowLink entities.
func (m *WorkflowLinkMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *WorkflowLinkMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *WorkflowLinkMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().WorkflowLink.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetWorkflowID sets the "workflow_id" field.
func (m *WorkflowLinkMutation) SetWorkflowID(u uuid.UUID) {
	m.workflow = &u
}

// WorkflowID returns the value of the "workflow_id" field in the mutation.
func (m *WorkflowLinkMutation) WorkflowID() (r uuid.UUID, exists bool) {
	v := m.workflow
	if v == nil {
		return
	}
	return *v, true
}

// OldWorkflowID returns the old "workflow_id" field's value of the WorkflowLink entity.
// If the WorkflowLink object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowLinkMutation) OldWorkflowID(ctx context.Context) (v uuid.UUID, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldWorkflowID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldWorkflowID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldWorkflowID: %w", err)
	}
	return oldValue.WorkflowID, nil
}

// ResetWorkflowID resets all changes to the "workflow_id" field.
func (m *WorkflowLinkMutation) ResetWorkflowID() {
	m.workflow = nil
}

// SetFromNodeID sets the "from_node_id" field.
func (m *WorkflowLinkMutation) SetFromNodeID(u uuid.UUID) {
	m.from_node_id = &u
}

// FromNodeID returns the value of the "from_node_id" field in the mutation.
func (m *WorkflowLinkMutation) FromNodeID() (r uuid.UUID, exists bool) {
	v := m.from_node_id
	if v == nil {
		return
	}
	return *v, true
}

// OldFromNodeID returns the old "from_node_id" field's value of the WorkflowLink entity.
// If the WorkflowLink object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowLinkMutation) OldFromNodeID(ctx context.Context) (v uuid.UUID, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldFromNodeID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldFromNodeID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldFromNodeID: %w", err)
	}
	return oldValue.FromNodeID, nil
}

// ResetFromNodeID resets all changes to the "from_node_id" field.
func (m *WorkflowLinkMutation) ResetFromNodeID() {
	m.from_node_id = nil
}

// SetToNodeID sets the "to_node_id" field.
func (m *WorkflowLinkMutation) SetToNodeID(u uuid.UUID) {
	m.to_node_id = &u
}

// ToNodeID returns the value of the "to_node_id" field in the mutation.
func (m *WorkflowLinkMutation) ToNodeID() (r uuid.UUID, exists bool) {
	v := m.to_node_id
	if v == nil {
		return
	}
	return *v, true
}

// OldToNodeID returns the old "to_node_id" field's value of the WorkflowLink entity.
// If the WorkflowLink object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowLinkMutation) OldToNodeID(ctx context.Context) (v uuid.UUID, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldToNodeID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldToNodeID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldToNodeID: %w", err)
	}
	return oldValue.ToNodeID, nil
}

// ResetToNodeID resets all changes to the "to_node_id" field.
func (m *WorkflowLinkMutation) ResetToNodeID() {
	m.to_node_id = nil
}

// SetFromPortID sets the "from_port_id" field.
func (m *WorkflowLinkMutation) SetFromPortID(s string) {
	m.from_port_id = &s
}

// FromPortID returns the value of the "from_port_id" field in the mutation.
func (m *WorkflowLinkMutation) FromPortID() (r string, exists bool) {
	v := m.from_port_id
	if v == nil {
		return
	}
	return *v, true
}

// OldFromPortID returns the old "from_port_id" field's value of the WorkflowLink entity.
// If the WorkflowLink object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowLinkMutation) OldFromPortID(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldFromPortID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldFromPortID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldFromPortID: %w", err)
	}
	return oldValue.FromPortID, nil
}

// ResetFromPortID resets all changes to the "from_port_id" field.
func (m *WorkflowLinkMutation) ResetFromPortID() {
	m.from_port_id = nil
}

// SetToPortID sets the "to_port_id" field.
func (m *WorkflowLinkMutation) SetToPortID(s string) {
	m.to_port_id = &s
}

// ToPortID returns the value of the "to_port_id" field in the mutation.
func (m *WorkflowLinkMutation) ToPortID() (r string, exists bool) {
	v := m.to_port_id
	if v == nil {
		return
	}
	return *v, true
}

// OldToPortID returns the old "to_port_id" field's value of the WorkflowLink entity.
// If the WorkflowLink object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowLinkMutation) OldToPortID(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldToPortID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldToPortID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldToPortID: %w", err)
	}
	return oldValue.ToPortID, nil
}

// ResetToPortID resets all changes to the "to_port_id" field.
func (m *WorkflowLinkMutation) ResetToPortID() {
	m.to_port_id = nil
}

// SetType sets the "type" field.
func (m *WorkflowLinkMutation) SetType(s string) {
	m._type = &s
}

// GetType returns the value of the "type" field in the mutation.
func (m *WorkflowLinkMutation) GetType() (r string, exists bool) {
	v := m._type
	if v == nil {
		return
	}
	return *v, true
}

// OldType returns the old "type" field's value of the WorkflowLink entity.
// If the WorkflowLink object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowLinkMutation) OldType(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldType: %w", err)
	}
	return oldValue.Type, nil
}

// ResetType resets all changes to the "type" field.
func (m *WorkflowLinkMutation) ResetType() {
	m._type = nil
}

// SetCreatedAt sets the "created_at" field.
func (m *WorkflowLinkMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *WorkflowLinkMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the WorkflowLink entity.
// If the WorkflowLink object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowLinkMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *WorkflowLinkMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *WorkflowLinkMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *WorkflowLinkMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the WorkflowLink entity.
// If the WorkflowLink object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowLinkMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *WorkflowLinkMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// ClearWorkflow clears the "workflow" edge to the Workflow entity.
func (m *WorkflowLinkMutation) ClearWorkflow() {
	m.clearedworkflow = true
	m.clearedFields[workflowlink.FieldWorkflowID] = struct{}{}
}

// WorkflowCleared reports if the "workflow" edge to the Workflow entity was cleared.
func (m *WorkflowLinkMutation) WorkflowCleared() bool {
	return m.clearedworkflow
}

// WorkflowIDs returns the "workflow" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// WorkflowID instead. It exists only for internal usage by the builders.
func (m *WorkflowLinkMutation) WorkflowIDs() (ids []uuid.UUID) {
	if id := m.workflow; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetWorkflow resets all changes to the "workflow" edge.
func (m *WorkflowLinkMutation) ResetWorkflow() {
	m.workflow = nil
	m.clearedworkflow = false
}

// Where appends a list predicates to the WorkflowLinkMutation builder.
func (m *WorkflowLinkMutation) Where(ps ...predicate.WorkflowLink) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the WorkflowLinkMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *WorkflowLinkMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.WorkflowLink, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *WorkflowLinkMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *WorkflowLinkMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (WorkflowLink).
func (m *WorkflowLinkMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *WorkflowLinkMutation) Fields() []string {
	fields := make([]string, 0, 8)
	if m.workflow != nil {
		fields = append(fields, workflowlink.FieldWorkflowID)
	}
	if m.from_node_id != nil {
		fields = append(fields, workflowlink.FieldFromNodeID)
	}
	if m.to_node_id != nil {
		fields = append(fields, workflowlink.FieldToNodeID)
	}
	if m.from_port_id != nil {
		fields = append(fields, workflowlink.FieldFromPortID)
	}
	if m.to_port_id != nil {
		fields = append(fields, workflowlink.FieldToPortID)
	}
	if m._type != nil {
		fields = append(fields, workflowlink.FieldType)
	}
	if m.created_at != nil {
		fields = append(fields, workflowlink.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, workflowlink.FieldUpdatedAt)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *WorkflowLinkMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case workflowlink.FieldWorkflowID:
		return m.WorkflowID()
	case workflowlink.FieldFromNodeID:
		return m.FromNodeID()
	case workflowlink.FieldToNodeID:
		return m.ToNodeID()
	case workflowlink.FieldFromPortID:
		return m.FromPortID()
	case workflowlink.FieldToPortID:
		return m.ToPortID()
	case workflowlink.FieldType:
		return m.GetType()
	case workflowlink.FieldCreatedAt:
		return m.CreatedAt()
	case workflowlink.FieldUpdatedAt:
		return m.UpdatedAt()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *WorkflowLinkMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case workflowlink.FieldWorkflowID:
		return m.OldWorkflowID(ctx)
	case workflowlink.FieldFromNodeID:
		return m.OldFromNodeID(ctx)
	case workflowlink.FieldToNodeID:
		return m.OldToNodeID(ctx)
	case workflowlink.FieldFromPortID:
		return m.OldFromPortID(ctx)
	case workflowlink.FieldToPortID:
		return m.OldToPortID(ctx)
	case workflowlink.FieldType:
		return m.OldType(ctx)
	case workflowlink.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case workflowlink.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	}
	return nil, fmt.Errorf("unknown WorkflowLink field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *WorkflowLinkMutation) SetField(name string, value ent.Value) error {
	switch name {
	case workflowlink.FieldWorkflowID:
		v, ok := value.(uuid.UUID)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetWorkflowID(v)
		return nil
	case workflowlink.FieldFromNodeID:
		v, ok := value.(uuid.UUID)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetFromNodeID(v)
		return nil
	case workflowlink.FieldToNodeID:
		v, ok := value.(uuid.UUID)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetToNodeID(v)
		return nil
	case workflowlink.FieldFromPortID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetFromPortID(v)
		return nil
	case workflowlink.FieldToPortID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetToPortID(v)
		return nil
	case workflowlink.FieldType:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetType(v)
		return nil
	case workflowlink.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case workflowlink.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	}
	return fmt.Errorf("unknown WorkflowLink field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *WorkflowLinkMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *WorkflowLinkMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *WorkflowLinkMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown WorkflowLink numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *WorkflowLinkMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *WorkflowLinkMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *WorkflowLinkMutation) ClearField(name string) error {
	return fmt.Errorf("unknown WorkflowLink nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *WorkflowLinkMutation) ResetField(name string) error {
	switch name {
	case workflowlink.FieldWorkflowID:
		m.ResetWorkflowID()
		return nil
	case workflowlink.FieldFromNodeID:
		m.ResetFromNodeID()
		return nil
	case workflowlink.FieldToNodeID:
		m.ResetToNodeID()
		return nil
	case workflowlink.FieldFromPortID:
		m.ResetFromPortID()
		return nil
	case workflowlink.FieldToPortID:
		m.ResetToPortID()
		return nil
	case workflowlink.FieldType:
		m.ResetType()
		return nil
	case workflowlink.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case workflowlink.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	}
	return fmt.Errorf("unknown WorkflowLink field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *WorkflowLinkMutation) AddedEdges() []string {
	edges := make([]string, 0, 1)
	if m.workflow != nil {
		edges = append(edges, workflowlink.EdgeWorkflow)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *WorkflowLinkMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case workflowlink.EdgeWorkflow:
		if id := m.workflow; id != nil {
			return []ent.Value{*id}
		}
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *WorkflowLinkMutation) RemovedEdges() []string {
	edges := make([]string, 0, 1)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *WorkflowLinkMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *WorkflowLinkMutation) ClearedEdges() []string {
	edges := make([]string, 0, 1)
	if m.clearedworkflow {
		edges = append(edges, workflowlink.EdgeWorkflow)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *WorkflowLinkMutation) EdgeCleared(name string) bool {
	switch name {
	case workflowlink.EdgeWorkflow:
		return m.clearedworkflow
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *WorkflowLinkMutation) ClearEdge(name string) error {
	switch name {
	case workflowlink.EdgeWorkflow:
		m.ClearWorkflow()
		return nil
	}
	return fmt.Errorf("unknown WorkflowLink unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *WorkflowLinkMutation) ResetEdge(name string) error {
	switch name {
	case workflowlink.EdgeWorkflow:
		m.ResetWorkflow()
		return nil
	}
	return fmt.Errorf("unknown WorkflowLink edge %s", name)
}

// WorkflowNodeMutation represents an operation that mutates the WorkflowNode nodes in the graph.
type WorkflowNodeMutation struct {
	config
	op                  Op
	typ                 string
	id                  *uuid.UUID
	name                *string
	description         *string
	icon                *string
	_type               *string
	version             *string
	plugin_name         *string
	plugin_version      *string
	input_params        *[]*v1.NodeParam
	appendinput_params  []*v1.NodeParam
	input_values        *json.RawMessage
	appendinput_values  json.RawMessage
	output_params       *[]*v1.NodeParam
	appendoutput_params []*v1.NodeParam
	output_values       *json.RawMessage
	appendoutput_values json.RawMessage
	input_ports         *[]*v1.NodePort
	appendinput_ports   []*v1.NodePort
	output_ports        *[]*v1.NodePort
	appendoutput_ports  []*v1.NodePort
	position            **v1.WorkflowNodePosition
	data                *json.RawMessage
	appenddata          json.RawMessage
	created_at          *time.Time
	updated_at          *time.Time
	clearedFields       map[string]struct{}
	workflow            *uuid.UUID
	clearedworkflow     bool
	done                bool
	oldValue            func(context.Context) (*WorkflowNode, error)
	predicates          []predicate.WorkflowNode
}

var _ ent.Mutation = (*WorkflowNodeMutation)(nil)

// workflownodeOption allows management of the mutation configuration using functional options.
type workflownodeOption func(*WorkflowNodeMutation)

// newWorkflowNodeMutation creates new mutation for the WorkflowNode entity.
func newWorkflowNodeMutation(c config, op Op, opts ...workflownodeOption) *WorkflowNodeMutation {
	m := &WorkflowNodeMutation{
		config:        c,
		op:            op,
		typ:           TypeWorkflowNode,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withWorkflowNodeID sets the ID field of the mutation.
func withWorkflowNodeID(id uuid.UUID) workflownodeOption {
	return func(m *WorkflowNodeMutation) {
		var (
			err   error
			once  sync.Once
			value *WorkflowNode
		)
		m.oldValue = func(ctx context.Context) (*WorkflowNode, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().WorkflowNode.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withWorkflowNode sets the old WorkflowNode of the mutation.
func withWorkflowNode(node *WorkflowNode) workflownodeOption {
	return func(m *WorkflowNodeMutation) {
		m.oldValue = func(context.Context) (*WorkflowNode, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m WorkflowNodeMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m WorkflowNodeMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of WorkflowNode entities.
func (m *WorkflowNodeMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *WorkflowNodeMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *WorkflowNodeMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().WorkflowNode.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetWorkflowID sets the "workflow_id" field.
func (m *WorkflowNodeMutation) SetWorkflowID(u uuid.UUID) {
	m.workflow = &u
}

// WorkflowID returns the value of the "workflow_id" field in the mutation.
func (m *WorkflowNodeMutation) WorkflowID() (r uuid.UUID, exists bool) {
	v := m.workflow
	if v == nil {
		return
	}
	return *v, true
}

// OldWorkflowID returns the old "workflow_id" field's value of the WorkflowNode entity.
// If the WorkflowNode object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowNodeMutation) OldWorkflowID(ctx context.Context) (v uuid.UUID, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldWorkflowID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldWorkflowID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldWorkflowID: %w", err)
	}
	return oldValue.WorkflowID, nil
}

// ResetWorkflowID resets all changes to the "workflow_id" field.
func (m *WorkflowNodeMutation) ResetWorkflowID() {
	m.workflow = nil
}

// SetName sets the "name" field.
func (m *WorkflowNodeMutation) SetName(s string) {
	m.name = &s
}

// Name returns the value of the "name" field in the mutation.
func (m *WorkflowNodeMutation) Name() (r string, exists bool) {
	v := m.name
	if v == nil {
		return
	}
	return *v, true
}

// OldName returns the old "name" field's value of the WorkflowNode entity.
// If the WorkflowNode object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowNodeMutation) OldName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldName: %w", err)
	}
	return oldValue.Name, nil
}

// ResetName resets all changes to the "name" field.
func (m *WorkflowNodeMutation) ResetName() {
	m.name = nil
}

// SetDescription sets the "description" field.
func (m *WorkflowNodeMutation) SetDescription(s string) {
	m.description = &s
}

// Description returns the value of the "description" field in the mutation.
func (m *WorkflowNodeMutation) Description() (r string, exists bool) {
	v := m.description
	if v == nil {
		return
	}
	return *v, true
}

// OldDescription returns the old "description" field's value of the WorkflowNode entity.
// If the WorkflowNode object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowNodeMutation) OldDescription(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDescription is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDescription requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDescription: %w", err)
	}
	return oldValue.Description, nil
}

// ResetDescription resets all changes to the "description" field.
func (m *WorkflowNodeMutation) ResetDescription() {
	m.description = nil
}

// SetIcon sets the "icon" field.
func (m *WorkflowNodeMutation) SetIcon(s string) {
	m.icon = &s
}

// Icon returns the value of the "icon" field in the mutation.
func (m *WorkflowNodeMutation) Icon() (r string, exists bool) {
	v := m.icon
	if v == nil {
		return
	}
	return *v, true
}

// OldIcon returns the old "icon" field's value of the WorkflowNode entity.
// If the WorkflowNode object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowNodeMutation) OldIcon(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIcon is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIcon requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIcon: %w", err)
	}
	return oldValue.Icon, nil
}

// ResetIcon resets all changes to the "icon" field.
func (m *WorkflowNodeMutation) ResetIcon() {
	m.icon = nil
}

// SetType sets the "type" field.
func (m *WorkflowNodeMutation) SetType(s string) {
	m._type = &s
}

// GetType returns the value of the "type" field in the mutation.
func (m *WorkflowNodeMutation) GetType() (r string, exists bool) {
	v := m._type
	if v == nil {
		return
	}
	return *v, true
}

// OldType returns the old "type" field's value of the WorkflowNode entity.
// If the WorkflowNode object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowNodeMutation) OldType(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldType: %w", err)
	}
	return oldValue.Type, nil
}

// ResetType resets all changes to the "type" field.
func (m *WorkflowNodeMutation) ResetType() {
	m._type = nil
}

// SetVersion sets the "version" field.
func (m *WorkflowNodeMutation) SetVersion(s string) {
	m.version = &s
}

// Version returns the value of the "version" field in the mutation.
func (m *WorkflowNodeMutation) Version() (r string, exists bool) {
	v := m.version
	if v == nil {
		return
	}
	return *v, true
}

// OldVersion returns the old "version" field's value of the WorkflowNode entity.
// If the WorkflowNode object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowNodeMutation) OldVersion(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldVersion is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldVersion requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldVersion: %w", err)
	}
	return oldValue.Version, nil
}

// ResetVersion resets all changes to the "version" field.
func (m *WorkflowNodeMutation) ResetVersion() {
	m.version = nil
}

// SetPluginName sets the "plugin_name" field.
func (m *WorkflowNodeMutation) SetPluginName(s string) {
	m.plugin_name = &s
}

// PluginName returns the value of the "plugin_name" field in the mutation.
func (m *WorkflowNodeMutation) PluginName() (r string, exists bool) {
	v := m.plugin_name
	if v == nil {
		return
	}
	return *v, true
}

// OldPluginName returns the old "plugin_name" field's value of the WorkflowNode entity.
// If the WorkflowNode object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowNodeMutation) OldPluginName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPluginName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPluginName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPluginName: %w", err)
	}
	return oldValue.PluginName, nil
}

// ResetPluginName resets all changes to the "plugin_name" field.
func (m *WorkflowNodeMutation) ResetPluginName() {
	m.plugin_name = nil
}

// SetPluginVersion sets the "plugin_version" field.
func (m *WorkflowNodeMutation) SetPluginVersion(s string) {
	m.plugin_version = &s
}

// PluginVersion returns the value of the "plugin_version" field in the mutation.
func (m *WorkflowNodeMutation) PluginVersion() (r string, exists bool) {
	v := m.plugin_version
	if v == nil {
		return
	}
	return *v, true
}

// OldPluginVersion returns the old "plugin_version" field's value of the WorkflowNode entity.
// If the WorkflowNode object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowNodeMutation) OldPluginVersion(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPluginVersion is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPluginVersion requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPluginVersion: %w", err)
	}
	return oldValue.PluginVersion, nil
}

// ResetPluginVersion resets all changes to the "plugin_version" field.
func (m *WorkflowNodeMutation) ResetPluginVersion() {
	m.plugin_version = nil
}

// SetInputParams sets the "input_params" field.
func (m *WorkflowNodeMutation) SetInputParams(vp []*v1.NodeParam) {
	m.input_params = &vp
	m.appendinput_params = nil
}

// InputParams returns the value of the "input_params" field in the mutation.
func (m *WorkflowNodeMutation) InputParams() (r []*v1.NodeParam, exists bool) {
	v := m.input_params
	if v == nil {
		return
	}
	return *v, true
}

// OldInputParams returns the old "input_params" field's value of the WorkflowNode entity.
// If the WorkflowNode object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowNodeMutation) OldInputParams(ctx context.Context) (v []*v1.NodeParam, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldInputParams is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldInputParams requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldInputParams: %w", err)
	}
	return oldValue.InputParams, nil
}

// AppendInputParams adds vp to the "input_params" field.
func (m *WorkflowNodeMutation) AppendInputParams(vp []*v1.NodeParam) {
	m.appendinput_params = append(m.appendinput_params, vp...)
}

// AppendedInputParams returns the list of values that were appended to the "input_params" field in this mutation.
func (m *WorkflowNodeMutation) AppendedInputParams() ([]*v1.NodeParam, bool) {
	if len(m.appendinput_params) == 0 {
		return nil, false
	}
	return m.appendinput_params, true
}

// ResetInputParams resets all changes to the "input_params" field.
func (m *WorkflowNodeMutation) ResetInputParams() {
	m.input_params = nil
	m.appendinput_params = nil
}

// SetInputValues sets the "input_values" field.
func (m *WorkflowNodeMutation) SetInputValues(jm json.RawMessage) {
	m.input_values = &jm
	m.appendinput_values = nil
}

// InputValues returns the value of the "input_values" field in the mutation.
func (m *WorkflowNodeMutation) InputValues() (r json.RawMessage, exists bool) {
	v := m.input_values
	if v == nil {
		return
	}
	return *v, true
}

// OldInputValues returns the old "input_values" field's value of the WorkflowNode entity.
// If the WorkflowNode object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowNodeMutation) OldInputValues(ctx context.Context) (v json.RawMessage, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldInputValues is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldInputValues requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldInputValues: %w", err)
	}
	return oldValue.InputValues, nil
}

// AppendInputValues adds jm to the "input_values" field.
func (m *WorkflowNodeMutation) AppendInputValues(jm json.RawMessage) {
	m.appendinput_values = append(m.appendinput_values, jm...)
}

// AppendedInputValues returns the list of values that were appended to the "input_values" field in this mutation.
func (m *WorkflowNodeMutation) AppendedInputValues() (json.RawMessage, bool) {
	if len(m.appendinput_values) == 0 {
		return nil, false
	}
	return m.appendinput_values, true
}

// ResetInputValues resets all changes to the "input_values" field.
func (m *WorkflowNodeMutation) ResetInputValues() {
	m.input_values = nil
	m.appendinput_values = nil
}

// SetOutputParams sets the "output_params" field.
func (m *WorkflowNodeMutation) SetOutputParams(vp []*v1.NodeParam) {
	m.output_params = &vp
	m.appendoutput_params = nil
}

// OutputParams returns the value of the "output_params" field in the mutation.
func (m *WorkflowNodeMutation) OutputParams() (r []*v1.NodeParam, exists bool) {
	v := m.output_params
	if v == nil {
		return
	}
	return *v, true
}

// OldOutputParams returns the old "output_params" field's value of the WorkflowNode entity.
// If the WorkflowNode object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowNodeMutation) OldOutputParams(ctx context.Context) (v []*v1.NodeParam, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldOutputParams is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldOutputParams requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldOutputParams: %w", err)
	}
	return oldValue.OutputParams, nil
}

// AppendOutputParams adds vp to the "output_params" field.
func (m *WorkflowNodeMutation) AppendOutputParams(vp []*v1.NodeParam) {
	m.appendoutput_params = append(m.appendoutput_params, vp...)
}

// AppendedOutputParams returns the list of values that were appended to the "output_params" field in this mutation.
func (m *WorkflowNodeMutation) AppendedOutputParams() ([]*v1.NodeParam, bool) {
	if len(m.appendoutput_params) == 0 {
		return nil, false
	}
	return m.appendoutput_params, true
}

// ResetOutputParams resets all changes to the "output_params" field.
func (m *WorkflowNodeMutation) ResetOutputParams() {
	m.output_params = nil
	m.appendoutput_params = nil
}

// SetOutputValues sets the "output_values" field.
func (m *WorkflowNodeMutation) SetOutputValues(jm json.RawMessage) {
	m.output_values = &jm
	m.appendoutput_values = nil
}

// OutputValues returns the value of the "output_values" field in the mutation.
func (m *WorkflowNodeMutation) OutputValues() (r json.RawMessage, exists bool) {
	v := m.output_values
	if v == nil {
		return
	}
	return *v, true
}

// OldOutputValues returns the old "output_values" field's value of the WorkflowNode entity.
// If the WorkflowNode object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowNodeMutation) OldOutputValues(ctx context.Context) (v json.RawMessage, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldOutputValues is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldOutputValues requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldOutputValues: %w", err)
	}
	return oldValue.OutputValues, nil
}

// AppendOutputValues adds jm to the "output_values" field.
func (m *WorkflowNodeMutation) AppendOutputValues(jm json.RawMessage) {
	m.appendoutput_values = append(m.appendoutput_values, jm...)
}

// AppendedOutputValues returns the list of values that were appended to the "output_values" field in this mutation.
func (m *WorkflowNodeMutation) AppendedOutputValues() (json.RawMessage, bool) {
	if len(m.appendoutput_values) == 0 {
		return nil, false
	}
	return m.appendoutput_values, true
}

// ResetOutputValues resets all changes to the "output_values" field.
func (m *WorkflowNodeMutation) ResetOutputValues() {
	m.output_values = nil
	m.appendoutput_values = nil
}

// SetInputPorts sets the "input_ports" field.
func (m *WorkflowNodeMutation) SetInputPorts(vp []*v1.NodePort) {
	m.input_ports = &vp
	m.appendinput_ports = nil
}

// InputPorts returns the value of the "input_ports" field in the mutation.
func (m *WorkflowNodeMutation) InputPorts() (r []*v1.NodePort, exists bool) {
	v := m.input_ports
	if v == nil {
		return
	}
	return *v, true
}

// OldInputPorts returns the old "input_ports" field's value of the WorkflowNode entity.
// If the WorkflowNode object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowNodeMutation) OldInputPorts(ctx context.Context) (v []*v1.NodePort, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldInputPorts is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldInputPorts requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldInputPorts: %w", err)
	}
	return oldValue.InputPorts, nil
}

// AppendInputPorts adds vp to the "input_ports" field.
func (m *WorkflowNodeMutation) AppendInputPorts(vp []*v1.NodePort) {
	m.appendinput_ports = append(m.appendinput_ports, vp...)
}

// AppendedInputPorts returns the list of values that were appended to the "input_ports" field in this mutation.
func (m *WorkflowNodeMutation) AppendedInputPorts() ([]*v1.NodePort, bool) {
	if len(m.appendinput_ports) == 0 {
		return nil, false
	}
	return m.appendinput_ports, true
}

// ResetInputPorts resets all changes to the "input_ports" field.
func (m *WorkflowNodeMutation) ResetInputPorts() {
	m.input_ports = nil
	m.appendinput_ports = nil
}

// SetOutputPorts sets the "output_ports" field.
func (m *WorkflowNodeMutation) SetOutputPorts(vp []*v1.NodePort) {
	m.output_ports = &vp
	m.appendoutput_ports = nil
}

// OutputPorts returns the value of the "output_ports" field in the mutation.
func (m *WorkflowNodeMutation) OutputPorts() (r []*v1.NodePort, exists bool) {
	v := m.output_ports
	if v == nil {
		return
	}
	return *v, true
}

// OldOutputPorts returns the old "output_ports" field's value of the WorkflowNode entity.
// If the WorkflowNode object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowNodeMutation) OldOutputPorts(ctx context.Context) (v []*v1.NodePort, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldOutputPorts is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldOutputPorts requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldOutputPorts: %w", err)
	}
	return oldValue.OutputPorts, nil
}

// AppendOutputPorts adds vp to the "output_ports" field.
func (m *WorkflowNodeMutation) AppendOutputPorts(vp []*v1.NodePort) {
	m.appendoutput_ports = append(m.appendoutput_ports, vp...)
}

// AppendedOutputPorts returns the list of values that were appended to the "output_ports" field in this mutation.
func (m *WorkflowNodeMutation) AppendedOutputPorts() ([]*v1.NodePort, bool) {
	if len(m.appendoutput_ports) == 0 {
		return nil, false
	}
	return m.appendoutput_ports, true
}

// ResetOutputPorts resets all changes to the "output_ports" field.
func (m *WorkflowNodeMutation) ResetOutputPorts() {
	m.output_ports = nil
	m.appendoutput_ports = nil
}

// SetPosition sets the "position" field.
func (m *WorkflowNodeMutation) SetPosition(vnp *v1.WorkflowNodePosition) {
	m.position = &vnp
}

// Position returns the value of the "position" field in the mutation.
func (m *WorkflowNodeMutation) Position() (r *v1.WorkflowNodePosition, exists bool) {
	v := m.position
	if v == nil {
		return
	}
	return *v, true
}

// OldPosition returns the old "position" field's value of the WorkflowNode entity.
// If the WorkflowNode object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowNodeMutation) OldPosition(ctx context.Context) (v *v1.WorkflowNodePosition, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPosition is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPosition requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPosition: %w", err)
	}
	return oldValue.Position, nil
}

// ResetPosition resets all changes to the "position" field.
func (m *WorkflowNodeMutation) ResetPosition() {
	m.position = nil
}

// SetData sets the "data" field.
func (m *WorkflowNodeMutation) SetData(jm json.RawMessage) {
	m.data = &jm
	m.appenddata = nil
}

// Data returns the value of the "data" field in the mutation.
func (m *WorkflowNodeMutation) Data() (r json.RawMessage, exists bool) {
	v := m.data
	if v == nil {
		return
	}
	return *v, true
}

// OldData returns the old "data" field's value of the WorkflowNode entity.
// If the WorkflowNode object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowNodeMutation) OldData(ctx context.Context) (v json.RawMessage, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldData is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldData requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldData: %w", err)
	}
	return oldValue.Data, nil
}

// AppendData adds jm to the "data" field.
func (m *WorkflowNodeMutation) AppendData(jm json.RawMessage) {
	m.appenddata = append(m.appenddata, jm...)
}

// AppendedData returns the list of values that were appended to the "data" field in this mutation.
func (m *WorkflowNodeMutation) AppendedData() (json.RawMessage, bool) {
	if len(m.appenddata) == 0 {
		return nil, false
	}
	return m.appenddata, true
}

// ClearData clears the value of the "data" field.
func (m *WorkflowNodeMutation) ClearData() {
	m.data = nil
	m.appenddata = nil
	m.clearedFields[workflownode.FieldData] = struct{}{}
}

// DataCleared returns if the "data" field was cleared in this mutation.
func (m *WorkflowNodeMutation) DataCleared() bool {
	_, ok := m.clearedFields[workflownode.FieldData]
	return ok
}

// ResetData resets all changes to the "data" field.
func (m *WorkflowNodeMutation) ResetData() {
	m.data = nil
	m.appenddata = nil
	delete(m.clearedFields, workflownode.FieldData)
}

// SetCreatedAt sets the "created_at" field.
func (m *WorkflowNodeMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *WorkflowNodeMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the WorkflowNode entity.
// If the WorkflowNode object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowNodeMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *WorkflowNodeMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *WorkflowNodeMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *WorkflowNodeMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the WorkflowNode entity.
// If the WorkflowNode object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *WorkflowNodeMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *WorkflowNodeMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// ClearWorkflow clears the "workflow" edge to the Workflow entity.
func (m *WorkflowNodeMutation) ClearWorkflow() {
	m.clearedworkflow = true
	m.clearedFields[workflownode.FieldWorkflowID] = struct{}{}
}

// WorkflowCleared reports if the "workflow" edge to the Workflow entity was cleared.
func (m *WorkflowNodeMutation) WorkflowCleared() bool {
	return m.clearedworkflow
}

// WorkflowIDs returns the "workflow" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// WorkflowID instead. It exists only for internal usage by the builders.
func (m *WorkflowNodeMutation) WorkflowIDs() (ids []uuid.UUID) {
	if id := m.workflow; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetWorkflow resets all changes to the "workflow" edge.
func (m *WorkflowNodeMutation) ResetWorkflow() {
	m.workflow = nil
	m.clearedworkflow = false
}

// Where appends a list predicates to the WorkflowNodeMutation builder.
func (m *WorkflowNodeMutation) Where(ps ...predicate.WorkflowNode) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the WorkflowNodeMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *WorkflowNodeMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.WorkflowNode, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *WorkflowNodeMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *WorkflowNodeMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (WorkflowNode).
func (m *WorkflowNodeMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *WorkflowNodeMutation) Fields() []string {
	fields := make([]string, 0, 18)
	if m.workflow != nil {
		fields = append(fields, workflownode.FieldWorkflowID)
	}
	if m.name != nil {
		fields = append(fields, workflownode.FieldName)
	}
	if m.description != nil {
		fields = append(fields, workflownode.FieldDescription)
	}
	if m.icon != nil {
		fields = append(fields, workflownode.FieldIcon)
	}
	if m._type != nil {
		fields = append(fields, workflownode.FieldType)
	}
	if m.version != nil {
		fields = append(fields, workflownode.FieldVersion)
	}
	if m.plugin_name != nil {
		fields = append(fields, workflownode.FieldPluginName)
	}
	if m.plugin_version != nil {
		fields = append(fields, workflownode.FieldPluginVersion)
	}
	if m.input_params != nil {
		fields = append(fields, workflownode.FieldInputParams)
	}
	if m.input_values != nil {
		fields = append(fields, workflownode.FieldInputValues)
	}
	if m.output_params != nil {
		fields = append(fields, workflownode.FieldOutputParams)
	}
	if m.output_values != nil {
		fields = append(fields, workflownode.FieldOutputValues)
	}
	if m.input_ports != nil {
		fields = append(fields, workflownode.FieldInputPorts)
	}
	if m.output_ports != nil {
		fields = append(fields, workflownode.FieldOutputPorts)
	}
	if m.position != nil {
		fields = append(fields, workflownode.FieldPosition)
	}
	if m.data != nil {
		fields = append(fields, workflownode.FieldData)
	}
	if m.created_at != nil {
		fields = append(fields, workflownode.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, workflownode.FieldUpdatedAt)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *WorkflowNodeMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case workflownode.FieldWorkflowID:
		return m.WorkflowID()
	case workflownode.FieldName:
		return m.Name()
	case workflownode.FieldDescription:
		return m.Description()
	case workflownode.FieldIcon:
		return m.Icon()
	case workflownode.FieldType:
		return m.GetType()
	case workflownode.FieldVersion:
		return m.Version()
	case workflownode.FieldPluginName:
		return m.PluginName()
	case workflownode.FieldPluginVersion:
		return m.PluginVersion()
	case workflownode.FieldInputParams:
		return m.InputParams()
	case workflownode.FieldInputValues:
		return m.InputValues()
	case workflownode.FieldOutputParams:
		return m.OutputParams()
	case workflownode.FieldOutputValues:
		return m.OutputValues()
	case workflownode.FieldInputPorts:
		return m.InputPorts()
	case workflownode.FieldOutputPorts:
		return m.OutputPorts()
	case workflownode.FieldPosition:
		return m.Position()
	case workflownode.FieldData:
		return m.Data()
	case workflownode.FieldCreatedAt:
		return m.CreatedAt()
	case workflownode.FieldUpdatedAt:
		return m.UpdatedAt()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *WorkflowNodeMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case workflownode.FieldWorkflowID:
		return m.OldWorkflowID(ctx)
	case workflownode.FieldName:
		return m.OldName(ctx)
	case workflownode.FieldDescription:
		return m.OldDescription(ctx)
	case workflownode.FieldIcon:
		return m.OldIcon(ctx)
	case workflownode.FieldType:
		return m.OldType(ctx)
	case workflownode.FieldVersion:
		return m.OldVersion(ctx)
	case workflownode.FieldPluginName:
		return m.OldPluginName(ctx)
	case workflownode.FieldPluginVersion:
		return m.OldPluginVersion(ctx)
	case workflownode.FieldInputParams:
		return m.OldInputParams(ctx)
	case workflownode.FieldInputValues:
		return m.OldInputValues(ctx)
	case workflownode.FieldOutputParams:
		return m.OldOutputParams(ctx)
	case workflownode.FieldOutputValues:
		return m.OldOutputValues(ctx)
	case workflownode.FieldInputPorts:
		return m.OldInputPorts(ctx)
	case workflownode.FieldOutputPorts:
		return m.OldOutputPorts(ctx)
	case workflownode.FieldPosition:
		return m.OldPosition(ctx)
	case workflownode.FieldData:
		return m.OldData(ctx)
	case workflownode.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case workflownode.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	}
	return nil, fmt.Errorf("unknown WorkflowNode field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *WorkflowNodeMutation) SetField(name string, value ent.Value) error {
	switch name {
	case workflownode.FieldWorkflowID:
		v, ok := value.(uuid.UUID)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetWorkflowID(v)
		return nil
	case workflownode.FieldName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetName(v)
		return nil
	case workflownode.FieldDescription:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDescription(v)
		return nil
	case workflownode.FieldIcon:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIcon(v)
		return nil
	case workflownode.FieldType:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetType(v)
		return nil
	case workflownode.FieldVersion:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetVersion(v)
		return nil
	case workflownode.FieldPluginName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPluginName(v)
		return nil
	case workflownode.FieldPluginVersion:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPluginVersion(v)
		return nil
	case workflownode.FieldInputParams:
		v, ok := value.([]*v1.NodeParam)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetInputParams(v)
		return nil
	case workflownode.FieldInputValues:
		v, ok := value.(json.RawMessage)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetInputValues(v)
		return nil
	case workflownode.FieldOutputParams:
		v, ok := value.([]*v1.NodeParam)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetOutputParams(v)
		return nil
	case workflownode.FieldOutputValues:
		v, ok := value.(json.RawMessage)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetOutputValues(v)
		return nil
	case workflownode.FieldInputPorts:
		v, ok := value.([]*v1.NodePort)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetInputPorts(v)
		return nil
	case workflownode.FieldOutputPorts:
		v, ok := value.([]*v1.NodePort)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetOutputPorts(v)
		return nil
	case workflownode.FieldPosition:
		v, ok := value.(*v1.WorkflowNodePosition)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPosition(v)
		return nil
	case workflownode.FieldData:
		v, ok := value.(json.RawMessage)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetData(v)
		return nil
	case workflownode.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case workflownode.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	}
	return fmt.Errorf("unknown WorkflowNode field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *WorkflowNodeMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *WorkflowNodeMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *WorkflowNodeMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown WorkflowNode numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *WorkflowNodeMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(workflownode.FieldData) {
		fields = append(fields, workflownode.FieldData)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *WorkflowNodeMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *WorkflowNodeMutation) ClearField(name string) error {
	switch name {
	case workflownode.FieldData:
		m.ClearData()
		return nil
	}
	return fmt.Errorf("unknown WorkflowNode nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *WorkflowNodeMutation) ResetField(name string) error {
	switch name {
	case workflownode.FieldWorkflowID:
		m.ResetWorkflowID()
		return nil
	case workflownode.FieldName:
		m.ResetName()
		return nil
	case workflownode.FieldDescription:
		m.ResetDescription()
		return nil
	case workflownode.FieldIcon:
		m.ResetIcon()
		return nil
	case workflownode.FieldType:
		m.ResetType()
		return nil
	case workflownode.FieldVersion:
		m.ResetVersion()
		return nil
	case workflownode.FieldPluginName:
		m.ResetPluginName()
		return nil
	case workflownode.FieldPluginVersion:
		m.ResetPluginVersion()
		return nil
	case workflownode.FieldInputParams:
		m.ResetInputParams()
		return nil
	case workflownode.FieldInputValues:
		m.ResetInputValues()
		return nil
	case workflownode.FieldOutputParams:
		m.ResetOutputParams()
		return nil
	case workflownode.FieldOutputValues:
		m.ResetOutputValues()
		return nil
	case workflownode.FieldInputPorts:
		m.ResetInputPorts()
		return nil
	case workflownode.FieldOutputPorts:
		m.ResetOutputPorts()
		return nil
	case workflownode.FieldPosition:
		m.ResetPosition()
		return nil
	case workflownode.FieldData:
		m.ResetData()
		return nil
	case workflownode.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case workflownode.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	}
	return fmt.Errorf("unknown WorkflowNode field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *WorkflowNodeMutation) AddedEdges() []string {
	edges := make([]string, 0, 1)
	if m.workflow != nil {
		edges = append(edges, workflownode.EdgeWorkflow)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *WorkflowNodeMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case workflownode.EdgeWorkflow:
		if id := m.workflow; id != nil {
			return []ent.Value{*id}
		}
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *WorkflowNodeMutation) RemovedEdges() []string {
	edges := make([]string, 0, 1)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *WorkflowNodeMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *WorkflowNodeMutation) ClearedEdges() []string {
	edges := make([]string, 0, 1)
	if m.clearedworkflow {
		edges = append(edges, workflownode.EdgeWorkflow)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *WorkflowNodeMutation) EdgeCleared(name string) bool {
	switch name {
	case workflownode.EdgeWorkflow:
		return m.clearedworkflow
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *WorkflowNodeMutation) ClearEdge(name string) error {
	switch name {
	case workflownode.EdgeWorkflow:
		m.ClearWorkflow()
		return nil
	}
	return fmt.Errorf("unknown WorkflowNode unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *WorkflowNodeMutation) ResetEdge(name string) error {
	switch name {
	case workflownode.EdgeWorkflow:
		m.ResetWorkflow()
		return nil
	}
	return fmt.Errorf("unknown WorkflowNode edge %s", name)
}
