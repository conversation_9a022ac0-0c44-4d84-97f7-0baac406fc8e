// Code generated by ent, DO NOT EDIT.

package nodedefinition

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/google/uuid"
)

const (
	// Label holds the string label denoting the nodedefinition type in the database.
	Label = "node_definition"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldPluginName holds the string denoting the plugin_name field in the database.
	FieldPluginName = "plugin_name"
	// FieldName holds the string denoting the name field in the database.
	FieldName = "name"
	// FieldAuthor holds the string denoting the author field in the database.
	FieldAuthor = "author"
	// FieldDescription holds the string denoting the description field in the database.
	FieldDescription = "description"
	// FieldIcon holds the string denoting the icon field in the database.
	FieldIcon = "icon"
	// FieldType holds the string denoting the type field in the database.
	FieldType = "type"
	// FieldVersion holds the string denoting the version field in the database.
	FieldVersion = "version"
	// FieldCategory holds the string denoting the category field in the database.
	FieldCategory = "category"
	// FieldInputParams holds the string denoting the input_params field in the database.
	FieldInputParams = "input_params"
	// FieldOutputParams holds the string denoting the output_params field in the database.
	FieldOutputParams = "output_params"
	// FieldInputPorts holds the string denoting the input_ports field in the database.
	FieldInputPorts = "input_ports"
	// FieldOutputPorts holds the string denoting the output_ports field in the database.
	FieldOutputPorts = "output_ports"
	// FieldException holds the string denoting the exception field in the database.
	FieldException = "exception"
	// FieldPath holds the string denoting the path field in the database.
	FieldPath = "path"
	// FieldBuiltin holds the string denoting the builtin field in the database.
	FieldBuiltin = "builtin"
	// FieldEnabled holds the string denoting the enabled field in the database.
	FieldEnabled = "enabled"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// Table holds the table name of the nodedefinition in the database.
	Table = "node_definitions"
)

// Columns holds all SQL columns for nodedefinition fields.
var Columns = []string{
	FieldID,
	FieldPluginName,
	FieldName,
	FieldAuthor,
	FieldDescription,
	FieldIcon,
	FieldType,
	FieldVersion,
	FieldCategory,
	FieldInputParams,
	FieldOutputParams,
	FieldInputPorts,
	FieldOutputPorts,
	FieldException,
	FieldPath,
	FieldBuiltin,
	FieldEnabled,
	FieldCreatedAt,
	FieldUpdatedAt,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// DefaultID holds the default value on creation for the "id" field.
	DefaultID func() uuid.UUID
)

// OrderOption defines the ordering options for the NodeDefinition queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByPluginName orders the results by the plugin_name field.
func ByPluginName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPluginName, opts...).ToFunc()
}

// ByName orders the results by the name field.
func ByName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldName, opts...).ToFunc()
}

// ByAuthor orders the results by the author field.
func ByAuthor(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAuthor, opts...).ToFunc()
}

// ByDescription orders the results by the description field.
func ByDescription(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDescription, opts...).ToFunc()
}

// ByIcon orders the results by the icon field.
func ByIcon(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIcon, opts...).ToFunc()
}

// ByType orders the results by the type field.
func ByType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldType, opts...).ToFunc()
}

// ByVersion orders the results by the version field.
func ByVersion(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldVersion, opts...).ToFunc()
}

// ByCategory orders the results by the category field.
func ByCategory(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCategory, opts...).ToFunc()
}

// ByException orders the results by the exception field.
func ByException(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldException, opts...).ToFunc()
}

// ByPath orders the results by the path field.
func ByPath(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPath, opts...).ToFunc()
}

// ByBuiltin orders the results by the builtin field.
func ByBuiltin(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldBuiltin, opts...).ToFunc()
}

// ByEnabled orders the results by the enabled field.
func ByEnabled(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldEnabled, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}
