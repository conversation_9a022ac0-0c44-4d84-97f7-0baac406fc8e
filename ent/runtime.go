// Code generated by ent, DO NOT EDIT.

package ent

import (
	"resflow/ent/nodedefinition"
	"resflow/ent/plugin"
	"resflow/ent/schema"
	"resflow/ent/user"
	"resflow/ent/workflow"
	"resflow/ent/workflowlink"
	"resflow/ent/workflownode"
	"time"

	"github.com/google/uuid"
)

// The init function reads all schema descriptors with runtime code
// (default values, validators, hooks and policies) and stitches it
// to their package variables.
func init() {
	nodedefinitionFields := schema.NodeDefinition{}.Fields()
	_ = nodedefinitionFields
	// nodedefinitionDescCreatedAt is the schema descriptor for created_at field.
	nodedefinitionDescCreatedAt := nodedefinitionFields[17].Descriptor()
	// nodedefinition.DefaultCreatedAt holds the default value on creation for the created_at field.
	nodedefinition.DefaultCreatedAt = nodedefinitionDescCreatedAt.Default.(func() time.Time)
	// nodedefinitionDescUpdatedAt is the schema descriptor for updated_at field.
	nodedefinitionDescUpdatedAt := nodedefinitionFields[18].Descriptor()
	// nodedefinition.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	nodedefinition.DefaultUpdatedAt = nodedefinitionDescUpdatedAt.Default.(func() time.Time)
	// nodedefinitionDescID is the schema descriptor for id field.
	nodedefinitionDescID := nodedefinitionFields[0].Descriptor()
	// nodedefinition.DefaultID holds the default value on creation for the id field.
	nodedefinition.DefaultID = nodedefinitionDescID.Default.(func() uuid.UUID)
	pluginFields := schema.Plugin{}.Fields()
	_ = pluginFields
	// pluginDescCreatedAt is the schema descriptor for created_at field.
	pluginDescCreatedAt := pluginFields[10].Descriptor()
	// plugin.DefaultCreatedAt holds the default value on creation for the created_at field.
	plugin.DefaultCreatedAt = pluginDescCreatedAt.Default.(func() time.Time)
	// pluginDescUpdatedAt is the schema descriptor for updated_at field.
	pluginDescUpdatedAt := pluginFields[11].Descriptor()
	// plugin.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	plugin.DefaultUpdatedAt = pluginDescUpdatedAt.Default.(func() time.Time)
	// pluginDescID is the schema descriptor for id field.
	pluginDescID := pluginFields[0].Descriptor()
	// plugin.DefaultID holds the default value on creation for the id field.
	plugin.DefaultID = pluginDescID.Default.(func() uuid.UUID)
	userFields := schema.User{}.Fields()
	_ = userFields
	// userDescUsername is the schema descriptor for username field.
	userDescUsername := userFields[1].Descriptor()
	// user.UsernameValidator is a validator for the "username" field. It is called by the builders before save.
	user.UsernameValidator = userDescUsername.Validators[0].(func(string) error)
	// userDescCreatedAt is the schema descriptor for created_at field.
	userDescCreatedAt := userFields[5].Descriptor()
	// user.DefaultCreatedAt holds the default value on creation for the created_at field.
	user.DefaultCreatedAt = userDescCreatedAt.Default.(func() time.Time)
	// userDescUpdatedAt is the schema descriptor for updated_at field.
	userDescUpdatedAt := userFields[6].Descriptor()
	// user.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	user.DefaultUpdatedAt = userDescUpdatedAt.Default.(func() time.Time)
	// userDescID is the schema descriptor for id field.
	userDescID := userFields[0].Descriptor()
	// user.DefaultID holds the default value on creation for the id field.
	user.DefaultID = userDescID.Default.(func() uuid.UUID)
	workflowFields := schema.Workflow{}.Fields()
	_ = workflowFields
	// workflowDescCreatedAt is the schema descriptor for created_at field.
	workflowDescCreatedAt := workflowFields[7].Descriptor()
	// workflow.DefaultCreatedAt holds the default value on creation for the created_at field.
	workflow.DefaultCreatedAt = workflowDescCreatedAt.Default.(func() time.Time)
	// workflowDescUpdatedAt is the schema descriptor for updated_at field.
	workflowDescUpdatedAt := workflowFields[8].Descriptor()
	// workflow.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	workflow.DefaultUpdatedAt = workflowDescUpdatedAt.Default.(func() time.Time)
	// workflowDescID is the schema descriptor for id field.
	workflowDescID := workflowFields[0].Descriptor()
	// workflow.DefaultID holds the default value on creation for the id field.
	workflow.DefaultID = workflowDescID.Default.(func() uuid.UUID)
	workflowlinkFields := schema.WorkflowLink{}.Fields()
	_ = workflowlinkFields
	// workflowlinkDescCreatedAt is the schema descriptor for created_at field.
	workflowlinkDescCreatedAt := workflowlinkFields[7].Descriptor()
	// workflowlink.DefaultCreatedAt holds the default value on creation for the created_at field.
	workflowlink.DefaultCreatedAt = workflowlinkDescCreatedAt.Default.(func() time.Time)
	// workflowlinkDescUpdatedAt is the schema descriptor for updated_at field.
	workflowlinkDescUpdatedAt := workflowlinkFields[8].Descriptor()
	// workflowlink.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	workflowlink.DefaultUpdatedAt = workflowlinkDescUpdatedAt.Default.(func() time.Time)
	// workflowlinkDescID is the schema descriptor for id field.
	workflowlinkDescID := workflowlinkFields[0].Descriptor()
	// workflowlink.DefaultID holds the default value on creation for the id field.
	workflowlink.DefaultID = workflowlinkDescID.Default.(func() uuid.UUID)
	workflownodeFields := schema.WorkflowNode{}.Fields()
	_ = workflownodeFields
	// workflownodeDescCreatedAt is the schema descriptor for created_at field.
	workflownodeDescCreatedAt := workflownodeFields[17].Descriptor()
	// workflownode.DefaultCreatedAt holds the default value on creation for the created_at field.
	workflownode.DefaultCreatedAt = workflownodeDescCreatedAt.Default.(func() time.Time)
	// workflownodeDescUpdatedAt is the schema descriptor for updated_at field.
	workflownodeDescUpdatedAt := workflownodeFields[18].Descriptor()
	// workflownode.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	workflownode.DefaultUpdatedAt = workflownodeDescUpdatedAt.Default.(func() time.Time)
	// workflownodeDescID is the schema descriptor for id field.
	workflownodeDescID := workflownodeFields[0].Descriptor()
	// workflownode.DefaultID holds the default value on creation for the id field.
	workflownode.DefaultID = workflownodeDescID.Default.(func() uuid.UUID)
}
