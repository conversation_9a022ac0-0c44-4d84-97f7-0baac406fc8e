package domain

import "time"

type AggregateRoot struct {
	id        string
	createdAt time.Time
	updatedAt time.Time
}

func NewAggregateRoot(id string, createdAt, updatedAt time.Time) AggregateRoot {
	return AggregateRoot{
		id:        id,
		createdAt: createdAt,
		updatedAt: updatedAt,
	}
}

func (ar AggregateRoot) ID() string {
	return ar.id
}

func (ar AggregateRoot) CreatedAt() time.Time {
	return ar.createdAt
}

func (ar AggregateRoot) UpdatedAt() time.Time {
	return ar.updatedAt
}
