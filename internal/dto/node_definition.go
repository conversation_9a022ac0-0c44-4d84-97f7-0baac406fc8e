package dto

import v1 "resflow/proto/generated_go/v1"

type NodeDefinitionDTO struct {
	ID            string          `json:"id" yaml:"id"`
	PluginName    string          `json:"plugin_name" yaml:"plugin_name"`
	PluginVersion string          `json:"plugin_version" yaml:"plugin_version"`
	Name          string          `json:"name" yaml:"name"`
	Author        string          `json:"author" yaml:"author"`
	Description   string          `json:"description" yaml:"description"`
	Icon          string          `json:"icon" yaml:"icon"`
	Type          string          `json:"type" yaml:"type"`
	Version       string          `json:"version" yaml:"version"`
	Category      string          `json:"category" yaml:"category"`
	InputParams   []*v1.NodeParam `json:"input_params" yaml:"input_params"`
	OutputParams  []*v1.NodeParam `json:"output_params" yaml:"output_params"`
	InputPorts    []*v1.NodePort  `json:"input_ports" yaml:"input_ports"`
	OutputPorts   []*v1.NodePort  `json:"output_ports" yaml:"output_ports"`
	Exception     bool            `json:"exception" yaml:"exception"`
	Path          string          `json:"path" yaml:"path"`
	Builtin       bool            `json:"builtin" yaml:"builtin"`
	Enabled       bool            `json:"enabled" yaml:"enabled"`
}

type NodeParam struct {
	Name        string                 `json:"name"`
	Type        string                 `json:"type"`
	ViewType    string                 `json:"view_type"`
	Label       string                 `json:"label"`
	Description string                 `json:"description"`
	Placeholder string                 `json:"placeholder"`
	Required    bool                   `json:"required"`
	Default     string                 `json:"default"`
	ViewConfig  map[string]interface{} `json:"view_config"`
}

type SelectOption struct {
	Label string `json:"label"`
	Value string `json:"value"`
}

type SelectOptions []*SelectOption

type MapSchema struct {
	KeyTitle   string `json:"key_title"`
	KeyType    string `json:"key_type"`
	ValueTitle string `json:"value_title"`
	ValueType  string `json:"value_type"`
}

type NodePort struct {
	Name     string `json:"name"`
	Type     string `json:"type"`
	Position string `json:"position"`
}
