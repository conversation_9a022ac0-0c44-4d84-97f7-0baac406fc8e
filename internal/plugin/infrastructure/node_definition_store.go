package infrastructure

import (
	"context"
	"errors"
	"resflow/ent"
	"resflow/ent/nodedefinition"
	"resflow/internal/plugin/domain"
)

var (
	ErrNodeDefinitionNotFound = errors.New("node definition not found")
)

type NodeDefinitionStore struct {
	client *ent.Client
}

func NewNodeDefinitionStore(client *ent.Client) domain.NodeDefinitionRepository {
	return &NodeDefinitionStore{client: client}
}

func (nds NodeDefinitionStore) FindByTypeAndVersion(ctx context.Context, pluginName, typeVar string, version string) (*domain.NodeDefinition, error) {
	nodeDefinition, err := nds.client.NodeDefinition.Query().Where(nodedefinition.PluginName(pluginName)).Where(nodedefinition.Type(typeVar), nodedefinition.Version(version)).First(ctx)
	if err != nil && !ent.IsNotFound(err) {
		return nil, err
	}
	if nodeDefinition == nil {
		return nil, nil
	}
	return entityToNodeDefinition(nodeDefinition), err
}

func (nds NodeDefinitionStore) FindLatestByType(ctx context.Context, pluginName, typeVar string) (*domain.NodeDefinition, error) {
	nodeDefinition, err := nds.client.NodeDefinition.Query().Where(nodedefinition.PluginName(pluginName)).Where(nodedefinition.Type(typeVar)).Where(nodedefinition.Enabled(true)).First(ctx)
	if err != nil && !ent.IsNotFound(err) {
		return nil, err
	}
	if nodeDefinition == nil {
		return nil, nil
	}
	return entityToNodeDefinition(nodeDefinition), err
}

func (nds NodeDefinitionStore) Save(ctx context.Context, nodeDefinition *domain.NodeDefinition) error {
	_, err := ent.TxFromContext(ctx).NodeDefinition.Create().SetPluginName(nodeDefinition.PluginName()).SetName(nodeDefinition.Name()).SetAuthor(nodeDefinition.Author()).SetDescription(nodeDefinition.Description()).SetIcon(nodeDefinition.Icon()).SetType(nodeDefinition.Type()).SetVersion(nodeDefinition.Version()).SetCategory(nodeDefinition.Category()).SetInputParams(nodeDefinition.InputParams()).SetOutputParams(nodeDefinition.OutputParams()).SetInputPorts(nodeDefinition.InputPorts()).SetOutputPorts(nodeDefinition.OutputPorts()).SetException(nodeDefinition.Exception()).SetPath(nodeDefinition.Path()).SetBuiltin(nodeDefinition.Builtin()).SetEnabled(nodeDefinition.Enabled()).Save(ctx)
	return err
}

func (nds NodeDefinitionStore) DisableByType(ctx context.Context, pluginName, typeVar string) error {
	return ent.TxFromContext(ctx).NodeDefinition.Update().Where(nodedefinition.PluginName(pluginName)).Where(nodedefinition.Type(typeVar)).SetEnabled(false).Exec(ctx)
}

func entityToNodeDefinition(nodeDefinition *ent.NodeDefinition) *domain.NodeDefinition {
	return domain.NewNodeDefinition(
		nodeDefinition.ID.String(),
		nodeDefinition.CreatedAt,
		nodeDefinition.UpdatedAt,
		nodeDefinition.PluginName,
		nodeDefinition.Name,
		nodeDefinition.Author,
		nodeDefinition.Description,
		nodeDefinition.Icon,
		nodeDefinition.Type,
		nodeDefinition.Version,
		nodeDefinition.Category,
		nodeDefinition.InputParams,
		nodeDefinition.OutputParams,
		nodeDefinition.InputPorts,
		nodeDefinition.OutputPorts,
		nodeDefinition.Exception,
		nodeDefinition.Path,
		nodeDefinition.Builtin,
		nodeDefinition.Enabled,
	)
}
