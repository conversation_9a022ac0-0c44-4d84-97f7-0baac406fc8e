package infrastructure

import (
	"context"
	"resflow/ent"
	"resflow/ent/plugin"
	"resflow/internal/plugin/domain"

	"entgo.io/ent/dialect/sql"
)

type PluginStore struct {
	client *ent.Client
}

func NewPluginStore(client *ent.Client) domain.PluginRepository {
	return &PluginStore{
		client: client,
	}
}

func (ps PluginStore) FindByNameAndVersion(ctx context.Context, name string, version string) (*domain.Plugin, error) {
	pluginEntity, err := ps.client.Plugin.Query().Where(plugin.Name(name)).Where(plugin.Version(version)).First(ctx)
	if err != nil && !ent.IsNotFound(err) {
		return nil, err
	}
	if pluginEntity == nil {
		return nil, nil
	}
	return entityToModel(pluginEntity)
}

func (ps PluginStore) Save(ctx context.Context, plugin *domain.Plugin) error {
	_, err := ent.TxFromContext(ctx).Plugin.Create().SetName(plugin.Name()).SetVersion(plugin.Version()).SetAuthor(plugin.Author()).SetDisplayName(plugin.DisplayName()).SetDescription(plugin.Description()).SetIcon(plugin.Icon()).SetPath(plugin.Path()).SetBuiltin(plugin.Builtin()).SetEnabled(plugin.Enabled()).Save(ctx)
	return err
}

func (ps PluginStore) List(ctx context.Context) ([]*domain.Plugin, error) {
	plugins, err := ps.client.Plugin.Query().Where(plugin.Enabled(true)).Order(plugin.ByCreatedAt(sql.OrderDesc())).All(ctx)
	if err != nil {
		return nil, err
	}
	return toPlugins(plugins)
}

func (ps PluginStore) DeactivateOtherVersion(ctx context.Context, version string) error {
	return ps.client.Plugin.Update().Where(plugin.Version(version)).SetEnabled(false).Exec(ctx)
}

func entityToModel(entity *ent.Plugin) (*domain.Plugin, error) {
	pluginModel, err := domain.NewPlugin(
		entity.ID.String(),
		entity.CreatedAt,
		entity.UpdatedAt,
		entity.Name,
		entity.Version,
		entity.Author,
		entity.DisplayName,
		entity.Description,
		entity.Icon,
		entity.Path,
		entity.Builtin,
		entity.Enabled,
	)
	if err != nil {
		return nil, err
	}
	return pluginModel, nil
}

func toPlugins(plugins []*ent.Plugin) ([]*domain.Plugin, error) {
	pluginEntities := make([]*domain.Plugin, 0)
	for _, p := range plugins {
		pluginModel, err := entityToModel(p)
		if err != nil {
			return nil, err
		}
		pluginEntities = append(pluginEntities, pluginModel)
	}
	return pluginEntities, nil
}
