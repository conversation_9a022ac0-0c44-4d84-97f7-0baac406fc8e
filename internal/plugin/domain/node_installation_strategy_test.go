package domain

import "testing"

func TestCompareVersions(t *testing.T) {
	cases := []struct {
		CaseDoc string
		V1      string
		V2      string
		Want    int
	}{
		{
			CaseDoc: "v1 > v2",
			V1:      "1.0.0",
			V2:      "0.9.9",
			Want:    1,
		},
		{
			CaseDoc: "v1 < v2",
			V1:      "0.9.9",
			V2:      "1.0.0",
			Want:    -1,
		},
		{
			CaseDoc: "v1 = v2",
			V1:      "1.0.0",
			V2:      "1.0.0",
			Want:    0,
		},
		{
			CaseDoc: "v1 = v2",
			V1:      "1.0.0",
			V2:      "1.0.0",
			Want:    0,
		},
		{
			CaseDoc: "v1 = v2",
			V1:      "1.0.0",
			V2:      "1.0.0",
			Want:    0,
		},
	}

	for _, tc := range cases {
		t.Run(tc.CaseDoc, func(t *testing.T) {
			got := CompareVersions(tc.V1, tc.V2)
			if got != tc.Want {
				t.<PERSON><PERSON>("want %d, got %d", tc.Want, got)
			}
		})
	}
}
