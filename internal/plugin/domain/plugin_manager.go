package domain

import (
	"context"
	"errors"
	"resflow/utils"
)

var (
	ErrPluginAlreadyInstalled = errors.New("plugin already installed")
)

type PluginManager struct {
	repository PluginRepository
}

func NewPluginManager(repository PluginRepository) *PluginManager {
	return &PluginManager{
		repository: repository,
	}
}

func (pm *PluginManager) Install(ctx context.Context, plugin *Plugin) error {
	exists, err := pm.repository.FindByNameAndVersion(ctx, plugin.Name(), plugin.Version().String())
	if err != nil {
		utils.Logger.Debugf("检查插件是否存在失败：%s", err.Error())
		return err
	}
	if exists != nil {
		utils.Logger.Debugf("插件已存在：%s", plugin.Name())
		return ErrPluginAlreadyInstalled
	}

	err = pm.EnablePlugin(ctx, plugin)
	if err != nil {
		utils.Logger.Debugf("启用插件失败：%s", err.Error())
		return err
	}
	return nil
}

func (pm *PluginManager) EnablePlugin(ctx context.Context, plugin *Plugin) error {
	err := pm.repository.DeactivateOtherVersion(ctx, plugin.Version().String())
	if err != nil {
		return err
	}
	plugin.Enable()
	return nil
}
