package domain

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewNodeDefinition(t *testing.T) {
	id := "test-node-id"
	createdAt := time.Now().Add(-time.Hour)
	updatedAt := time.Now()
	pluginName := "test-plugin"
	name := "Test Node"
	author := "test-author"
	description := "A test node definition"
	icon := "test-icon.png"
	nodeType := "processor"
	category := "data"
	path := "/path/to/node"
	builtin := true
	enabled := false
	exception := true

	// 创建测试参数和端口
	inputParams := []*NodeParam{
		{
			Name:        "input1",
			Type:        "string",
			ViewType:    "text",
			Label:       "Input 1",
			Description: "First input parameter",
			Required:    true,
			Default:     "default_value",
		},
	}
	outputParams := []*NodeParam{
		{
			Name:        "output1",
			Type:        "string",
			ViewType:    "text",
			Label:       "Output 1",
			Description: "First output parameter",
			Required:    false,
		},
	}
	inputPorts := []*NodePort{
		{
			Name:     "in1",
			Type:     "data",
			Position: "left",
		},
	}
	outputPorts := []*NodePort{
		{
			Name:     "out1",
			Type:     "data",
			Position: "right",
		},
	}

	tests := []struct {
		name       string
		versionStr string
		wantErr    bool
		wantErrMsg error
	}{
		{
			name:       "成功创建节点定义",
			versionStr: "1.0.0",
			wantErr:    false,
		},
		{
			name:       "版本格式错误",
			versionStr: "invalid-version",
			wantErr:    true,
			wantErrMsg: ErrInvalidNodeDefinitionVersionFormat,
		},
		{
			name:       "版本长度错误",
			versionStr: "1.0",
			wantErr:    true,
			wantErrMsg: ErrInvalidNodeDefinitionVersionFormat,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			nodeDef, err := NewNodeDefinition(
				id, createdAt, updatedAt, pluginName, name, author, description, icon,
				nodeType, tt.versionStr, category, inputParams, outputParams, inputPorts, outputPorts,
				exception, path, builtin, enabled,
			)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Equal(t, tt.wantErrMsg, err)
				assert.Nil(t, nodeDef)
				return
			}

			require.NoError(t, err)
			require.NotNil(t, nodeDef)

			// 验证所有属性
			assert.Equal(t, id, nodeDef.ID())
			assert.Equal(t, createdAt, nodeDef.CreatedAt())
			assert.Equal(t, updatedAt, nodeDef.UpdatedAt())
			assert.Equal(t, pluginName, nodeDef.PluginName())
			assert.Equal(t, name, nodeDef.Name())
			assert.Equal(t, author, nodeDef.Author())
			assert.Equal(t, description, nodeDef.Description())
			assert.Equal(t, icon, nodeDef.Icon())
			assert.Equal(t, nodeType, nodeDef.Type())
			assert.Equal(t, tt.versionStr, nodeDef.Version().String())
			assert.Equal(t, category, nodeDef.Category())
			assert.Equal(t, inputParams, nodeDef.InputParams())
			assert.Equal(t, outputParams, nodeDef.OutputParams())
			assert.Equal(t, inputPorts, nodeDef.InputPorts())
			assert.Equal(t, outputPorts, nodeDef.OutputPorts())
			assert.Equal(t, exception, nodeDef.Exception())
			assert.Equal(t, path, nodeDef.Path())
			assert.Equal(t, builtin, nodeDef.Builtin())
			assert.Equal(t, enabled, nodeDef.Enabled())
		})
	}
}

func TestNewNodeDefinitionFromManifest(t *testing.T) {
	// 创建测试插件
	pluginManifest := &PluginManifest{
		Name:    "test-plugin",
		Version: "1.0.0",
	}
	plugin, err := NewPluginFromManifest(pluginManifest, "/plugin/path", true)
	require.NoError(t, err)

	tests := []struct {
		name     string
		plugin   *Plugin
		manifest *NodeDefinitionManifest
		path     string
		wantErr  bool
		errMsg   string
	}{
		{
			name:   "成功创建节点定义",
			plugin: plugin,
			manifest: &NodeDefinitionManifest{
				Name:        "Test Node",
				Author:      "test-author",
				Version:     "1.0.0",
				Description: "A test node",
				Icon:        "test-icon.png",
				Type:        "processor",
				Category:    "data",
				InputParams: []*NodeParam{
					{
						Name:     "input1",
						Type:     "string",
						Required: true,
					},
				},
				OutputParams: []*NodeParam{
					{
						Name: "output1",
						Type: "string",
					},
				},
				InputPorts: []*NodePort{
					{
						Name:     "in1",
						Type:     "data",
						Position: "left",
					},
				},
				OutputPorts: []*NodePort{
					{
						Name:     "out1",
						Type:     "data",
						Position: "right",
					},
				},
				Exception: false,
			},
			path:    "/node/path",
			wantErr: false,
		},
		{
			name:   "节点类型为空",
			plugin: plugin,
			manifest: &NodeDefinitionManifest{
				Name:    "Test Node",
				Version: "1.0.0",
				Type:    "", // 空类型
			},
			path:    "/node/path",
			wantErr: true,
			errMsg:  ErrNodeDefinitionTypeAndVersionRequired.Error(),
		},
		{
			name:   "节点版本为空",
			plugin: plugin,
			manifest: &NodeDefinitionManifest{
				Name:    "Test Node",
				Type:    "processor",
				Version: "", // 空版本
			},
			path:    "/node/path",
			wantErr: true,
			errMsg:  ErrNodeDefinitionTypeAndVersionRequired.Error(),
		},
		{
			name:   "版本格式错误",
			plugin: plugin,
			manifest: &NodeDefinitionManifest{
				Name:    "Test Node",
				Type:    "processor",
				Version: "invalid-version", // 无效版本格式
			},
			path:    "/node/path",
			wantErr: true,
			errMsg:  ErrInvalidNodeDefinitionVersionFormat.Error(),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			nodeDef, err := NewNodeDefinitionFromManifest(tt.plugin, tt.manifest, tt.path)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.errMsg != "" {
					assert.Contains(t, err.Error(), tt.errMsg)
				}
				assert.Nil(t, nodeDef)
				return
			}

			require.NoError(t, err)
			require.NotNil(t, nodeDef)

			// 验证基本属性
			assert.Equal(t, tt.plugin.Name(), nodeDef.PluginName())
			assert.Equal(t, tt.manifest.Name, nodeDef.Name())
			assert.Equal(t, tt.manifest.Author, nodeDef.Author())
			assert.Equal(t, tt.manifest.Description, nodeDef.Description())
			assert.Equal(t, tt.manifest.Icon, nodeDef.Icon())
			assert.Equal(t, tt.manifest.Type, nodeDef.Type())
			assert.Equal(t, tt.manifest.Version, nodeDef.Version().String())
			assert.Equal(t, tt.manifest.Category, nodeDef.Category())
			assert.Equal(t, tt.manifest.InputParams, nodeDef.InputParams())
			assert.Equal(t, tt.manifest.OutputParams, nodeDef.OutputParams())
			assert.Equal(t, tt.manifest.InputPorts, nodeDef.InputPorts())
			assert.Equal(t, tt.manifest.OutputPorts, nodeDef.OutputPorts())
			assert.Equal(t, tt.manifest.Exception, nodeDef.Exception())
			assert.Equal(t, tt.path, nodeDef.Path())
			assert.Equal(t, tt.plugin.Builtin(), nodeDef.Builtin())
			assert.True(t, nodeDef.Enabled()) // 默认启用
			assert.NotEmpty(t, nodeDef.ID())
		})
	}
}

func TestNodeDefinition_Activate(t *testing.T) {
	// 创建一个禁用的节点定义
	nodeDef, err := NewNodeDefinition(
		"test-id", time.Now(), time.Now(),
		"test-plugin", "Test Node", "author", "desc", "icon",
		"processor", "1.0.0", "data",
		[]*NodeParam{}, []*NodeParam{},
		[]*NodePort{}, []*NodePort{},
		false, "/path", false, false, // enabled = false
	)
	require.NoError(t, err)
	require.NotNil(t, nodeDef)

	// 验证初始状态
	assert.False(t, nodeDef.Enabled())

	// 激活节点定义
	nodeDef.Activate()

	// 验证激活后的状态
	assert.True(t, nodeDef.Enabled())
}

func TestNodeDefinition_Getters(t *testing.T) {
	id := "test-node-id"
	createdAt := time.Now().Add(-time.Hour)
	updatedAt := time.Now()
	pluginName := "test-plugin"
	name := "Test Node"
	author := "test-author"
	description := "A comprehensive test node"
	icon := "test-icon.png"
	nodeType := "transformer"
	version := "2.1.0"
	category := "processing"
	path := "/path/to/node"
	builtin := false
	enabled := true
	exception := true

	inputParams := []*NodeParam{
		{
			Name:        "param1",
			Type:        "number",
			ViewType:    "slider",
			Label:       "Parameter 1",
			Description: "First parameter",
			Placeholder: "Enter number",
			Required:    true,
			Default:     "10",
			ViewConfig:  map[string]interface{}{"min": 0, "max": 100},
		},
	}
	outputParams := []*NodeParam{
		{
			Name:        "result",
			Type:        "string",
			ViewType:    "text",
			Label:       "Result",
			Description: "Processing result",
			Required:    false,
		},
	}
	inputPorts := []*NodePort{
		{
			Name:     "input_data",
			Type:     "stream",
			Position: "left",
		},
	}
	outputPorts := []*NodePort{
		{
			Name:     "output_data",
			Type:     "stream",
			Position: "right",
		},
	}

	nodeDef, err := NewNodeDefinition(
		id, createdAt, updatedAt, pluginName, name, author, description, icon,
		nodeType, version, category, inputParams, outputParams, inputPorts, outputPorts,
		exception, path, builtin, enabled,
	)
	require.NoError(t, err)
	require.NotNil(t, nodeDef)

	// 测试所有 getter 方法
	assert.Equal(t, id, nodeDef.ID())
	assert.Equal(t, createdAt, nodeDef.CreatedAt())
	assert.Equal(t, updatedAt, nodeDef.UpdatedAt())
	assert.Equal(t, pluginName, nodeDef.PluginName())
	assert.Equal(t, name, nodeDef.Name())
	assert.Equal(t, author, nodeDef.Author())
	assert.Equal(t, description, nodeDef.Description())
	assert.Equal(t, icon, nodeDef.Icon())
	assert.Equal(t, nodeType, nodeDef.Type())
	assert.Equal(t, version, nodeDef.Version().String())
	assert.Equal(t, category, nodeDef.Category())
	assert.Equal(t, inputParams, nodeDef.InputParams())
	assert.Equal(t, outputParams, nodeDef.OutputParams())
	assert.Equal(t, inputPorts, nodeDef.InputPorts())
	assert.Equal(t, outputPorts, nodeDef.OutputPorts())
	assert.Equal(t, exception, nodeDef.Exception())
	assert.Equal(t, path, nodeDef.Path())
	assert.Equal(t, builtin, nodeDef.Builtin())
	assert.Equal(t, enabled, nodeDef.Enabled())
}

func TestNodeParam_Structure(t *testing.T) {
	param := &NodeParam{
		Name:        "test_param",
		Type:        "string",
		ViewType:    "textarea",
		Label:       "Test Parameter",
		Description: "A test parameter for validation",
		Placeholder: "Enter your text here",
		Required:    true,
		Default:     "default_value",
		ViewConfig: map[string]interface{}{
			"rows":      5,
			"maxLength": 1000,
			"pattern":   "^[a-zA-Z0-9]*$",
		},
	}

	// 验证所有字段
	assert.Equal(t, "test_param", param.Name)
	assert.Equal(t, "string", param.Type)
	assert.Equal(t, "textarea", param.ViewType)
	assert.Equal(t, "Test Parameter", param.Label)
	assert.Equal(t, "A test parameter for validation", param.Description)
	assert.Equal(t, "Enter your text here", param.Placeholder)
	assert.True(t, param.Required)
	assert.Equal(t, "default_value", param.Default)
	assert.NotNil(t, param.ViewConfig)
	assert.Equal(t, 5, param.ViewConfig["rows"])
	assert.Equal(t, 1000, param.ViewConfig["maxLength"])
	assert.Equal(t, "^[a-zA-Z0-9]*$", param.ViewConfig["pattern"])
}

func TestNodePort_Structure(t *testing.T) {
	port := &NodePort{
		Name:     "data_input",
		Type:     "stream",
		Position: "left",
	}

	// 验证所有字段
	assert.Equal(t, "data_input", port.Name)
	assert.Equal(t, "stream", port.Type)
	assert.Equal(t, "left", port.Position)
}

func TestNodeDefinition_EmptyCollections(t *testing.T) {
	// 测试空集合的处理
	nodeDef, err := NewNodeDefinition(
		"test-id", time.Now(), time.Now(),
		"test-plugin", "Test Node", "author", "desc", "icon",
		"processor", "1.0.0", "data",
		nil, nil, nil, nil, // 所有集合都为 nil
		false, "/path", false, true,
	)
	require.NoError(t, err)
	require.NotNil(t, nodeDef)

	// 验证空集合不会导致 panic
	assert.Nil(t, nodeDef.InputParams())
	assert.Nil(t, nodeDef.OutputParams())
	assert.Nil(t, nodeDef.InputPorts())
	assert.Nil(t, nodeDef.OutputPorts())
}

func TestNodeDefinition_WithComplexViewConfig(t *testing.T) {
	// 测试复杂的 ViewConfig
	complexViewConfig := map[string]interface{}{
		"type":        "select",
		"multiple":    true,
		"options":     []string{"option1", "option2", "option3"},
		"validation":  map[string]interface{}{"required": true, "minItems": 1},
		"ui":          map[string]interface{}{"widget": "checkboxes", "inline": false},
		"conditional": map[string]interface{}{"field": "enable_feature", "value": true},
	}

	param := &NodeParam{
		Name:       "complex_param",
		Type:       "array",
		ViewType:   "multi-select",
		Required:   true,
		ViewConfig: complexViewConfig,
	}

	nodeDef, err := NewNodeDefinition(
		"test-id", time.Now(), time.Now(),
		"test-plugin", "Test Node", "author", "desc", "icon",
		"processor", "1.0.0", "data",
		[]*NodeParam{param}, []*NodeParam{},
		[]*NodePort{}, []*NodePort{},
		false, "/path", false, true,
	)
	require.NoError(t, err)
	require.NotNil(t, nodeDef)

	inputParams := nodeDef.InputParams()
	require.Len(t, inputParams, 1)

	retrievedParam := inputParams[0]
	assert.Equal(t, "complex_param", retrievedParam.Name)
	assert.Equal(t, "array", retrievedParam.Type)
	assert.Equal(t, "multi-select", retrievedParam.ViewType)
	assert.True(t, retrievedParam.Required)

	// 验证复杂的 ViewConfig
	assert.Equal(t, "select", retrievedParam.ViewConfig["type"])
	assert.Equal(t, true, retrievedParam.ViewConfig["multiple"])
	assert.Equal(t, []string{"option1", "option2", "option3"}, retrievedParam.ViewConfig["options"])

	validation, ok := retrievedParam.ViewConfig["validation"].(map[string]interface{})
	assert.True(t, ok)
	assert.Equal(t, true, validation["required"])
	assert.Equal(t, 1, validation["minItems"])
}

func TestNodeDefinition_Version(t *testing.T) {
	tests := []struct {
		name       string
		versionStr string
		wantErr    bool
		wantMajor  int
		wantMinor  int
		wantPatch  int
	}{
		{
			name:       "有效版本 1.0.0",
			versionStr: "1.0.0",
			wantErr:    false,
			wantMajor:  1,
			wantMinor:  0,
			wantPatch:  0,
		},
		{
			name:       "有效版本 2.5.3",
			versionStr: "2.5.3",
			wantErr:    false,
			wantMajor:  2,
			wantMinor:  5,
			wantPatch:  3,
		},
		{
			name:       "无效版本格式",
			versionStr: "invalid",
			wantErr:    true,
		},
		{
			name:       "版本长度不足",
			versionStr: "1.0",
			wantErr:    true,
		},
		{
			name:       "版本长度过长",
			versionStr: "1.0.0.0",
			wantErr:    true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			nodeDef, err := NewNodeDefinition(
				"test-id", time.Now(), time.Now(),
				"test-plugin", "Test Node", "author", "desc", "icon",
				"processor", tt.versionStr, "data",
				[]*NodeParam{}, []*NodeParam{},
				[]*NodePort{}, []*NodePort{},
				false, "/path", false, true,
			)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Equal(t, ErrInvalidNodeDefinitionVersionFormat, err)
				assert.Nil(t, nodeDef)
				return
			}

			require.NoError(t, err)
			require.NotNil(t, nodeDef)

			version := nodeDef.Version()
			require.NotNil(t, version)
			assert.Equal(t, tt.wantMajor, version.Major)
			assert.Equal(t, tt.wantMinor, version.Minor)
			assert.Equal(t, tt.wantPatch, version.Patch)
			assert.Equal(t, tt.versionStr, version.String())
		})
	}
}

func TestNodeDefinition_ErrorConstants(t *testing.T) {
	// 测试错误常量的定义
	assert.NotEmpty(t, ErrInvalidNodeDefinitionVersionFormat.Error())
	assert.NotEmpty(t, ErrNodeDefinitionTypeAndVersionRequired.Error())

	// 验证错误信息内容
	assert.Contains(t, ErrInvalidNodeDefinitionVersionFormat.Error(), "version")
	assert.Contains(t, ErrNodeDefinitionTypeAndVersionRequired.Error(), "type")
	assert.Contains(t, ErrNodeDefinitionTypeAndVersionRequired.Error(), "version")
}
