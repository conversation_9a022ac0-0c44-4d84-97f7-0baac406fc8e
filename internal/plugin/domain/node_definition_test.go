package domain

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewNodeDefinition(t *testing.T) {
	id := "test-node-id"
	createdAt := time.Now().Add(-time.Hour)
	updatedAt := time.Now()
	pluginName := "test-plugin"
	name := "Test Node"
	author := "test-author"
	description := "A test node definition"
	icon := "test-icon.png"
	category := "data"
	path := "/path/to/node"
	builtin := true
	enabled := false
	exception := true

	// 创建测试参数和端口
	inputParams := []*NodeParam{
		{
			Name:        "input1",
			Type:        "string",
			ViewType:    "text",
			Label:       "Input 1",
			Description: "First input parameter",
			Required:    true,
			Default:     "default_value",
		},
	}
	outputParams := []*NodeParam{
		{
			Name:        "output1",
			Type:        "string",
			ViewType:    "text",
			Label:       "Output 1",
			Description: "First output parameter",
			Required:    false,
		},
	}
	inputPorts := []*NodePort{
		{
			Name:     "in1",
			Type:     "data",
			Position: "left",
		},
	}
	outputPorts := []*NodePort{
		{
			Name:     "out1",
			Type:     "data",
			Position: "right",
		},
	}

	tests := []struct {
		name       string
		nodeType   string
		versionStr string
		wantErr    error
	}{
		{
			name:       "成功创建节点定义",
			nodeType:   "processor",
			versionStr: "1.0.0",
			wantErr:    nil,
		},
		{
			name:       "节点类型为空",
			nodeType:   "",
			versionStr: "1.0.0",
			wantErr:    ErrNodeDefinitionTypeAndVersionRequired,
		},
		{
			name:       "版本为空",
			nodeType:   "processor",
			versionStr: "",
			wantErr:    ErrNodeDefinitionTypeAndVersionRequired,
		},
		{
			name:       "版本格式错误",
			nodeType:   "processor",
			versionStr: "invalid-version",
			wantErr:    ErrInvalidNodeDefinitionVersionFormat,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			nodeDef, err := NewNodeDefinition(
				id, createdAt, updatedAt, pluginName, name, author, description, icon,
				tt.nodeType, tt.versionStr, category, inputParams, outputParams, inputPorts, outputPorts,
				exception, path, builtin, enabled,
			)

			if tt.wantErr != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.wantErr, err)
				assert.Nil(t, nodeDef)
				return
			}

			require.NoError(t, err)
			require.NotNil(t, nodeDef)

			// 验证所有属性
			assert.Equal(t, id, nodeDef.ID())
			assert.Equal(t, createdAt, nodeDef.CreatedAt())
			assert.Equal(t, updatedAt, nodeDef.UpdatedAt())
			assert.Equal(t, pluginName, nodeDef.PluginName())
			assert.Equal(t, name, nodeDef.Name())
			assert.Equal(t, author, nodeDef.Author())
			assert.Equal(t, description, nodeDef.Description())
			assert.Equal(t, icon, nodeDef.Icon())
			assert.Equal(t, tt.nodeType, nodeDef.Type())
			assert.Equal(t, tt.versionStr, nodeDef.Version().String())
			assert.Equal(t, category, nodeDef.Category())
			assert.Equal(t, inputParams, nodeDef.InputParams())
			assert.Equal(t, outputParams, nodeDef.OutputParams())
			assert.Equal(t, inputPorts, nodeDef.InputPorts())
			assert.Equal(t, outputPorts, nodeDef.OutputPorts())
			assert.Equal(t, exception, nodeDef.Exception())
			assert.Equal(t, path, nodeDef.Path())
			assert.Equal(t, builtin, nodeDef.Builtin())
			assert.Equal(t, enabled, nodeDef.Enabled())
		})
	}
}

func TestNewNodeDefinitionFromManifest(t *testing.T) {
	pluginManifest := &PluginManifest{
		Name:    "test-plugin",
		Version: "1.0.0",
	}
	plugin, err := NewPluginFromManifest(pluginManifest, "/plugin/path", true)
	require.NoError(t, err)

	tests := []struct {
		name     string
		plugin   *Plugin
		manifest *NodeDefinitionManifest
		path     string
		wantErr  error
	}{
		{
			name:   "成功创建节点定义",
			plugin: plugin,
			manifest: &NodeDefinitionManifest{
				Name:        "Test Node",
				Author:      "test-author",
				Version:     "1.0.0",
				Description: "A test node",
				Icon:        "test-icon.png",
				Type:        "processor",
				Category:    "data",
				InputParams: []*NodeParam{
					{
						Name:     "input1",
						Type:     "string",
						Required: true,
					},
				},
				OutputParams: []*NodeParam{
					{
						Name: "output1",
						Type: "string",
					},
				},
				InputPorts: []*NodePort{
					{
						Name:     "in1",
						Type:     "data",
						Position: "left",
					},
				},
				OutputPorts: []*NodePort{
					{
						Name:     "out1",
						Type:     "data",
						Position: "right",
					},
				},
				Exception: false,
			},
			path:    "/node/path",
			wantErr: nil,
		},
		{
			name:   "节点类型为空",
			plugin: plugin,
			manifest: &NodeDefinitionManifest{
				Name:    "Test Node",
				Version: "1.0.0",
				Type:    "", // 空类型
			},
			path:    "/node/path",
			wantErr: ErrNodeDefinitionTypeAndVersionRequired,
		},
		{
			name:   "节点版本为空",
			plugin: plugin,
			manifest: &NodeDefinitionManifest{
				Name:    "Test Node",
				Type:    "processor",
				Version: "", // 空版本
			},
			path:    "/node/path",
			wantErr: ErrNodeDefinitionTypeAndVersionRequired,
		},
		{
			name:   "版本格式错误",
			plugin: plugin,
			manifest: &NodeDefinitionManifest{
				Name:    "Test Node",
				Type:    "processor",
				Version: "invalid-version", // 无效版本格式
			},
			path:    "/node/path",
			wantErr: ErrInvalidNodeDefinitionVersionFormat,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			nodeDef, err := NewNodeDefinitionFromManifest(tt.plugin, tt.manifest, tt.path)

			if tt.wantErr != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.wantErr, err)
				assert.Nil(t, nodeDef)
				return
			}

			require.NoError(t, err)
			require.NotNil(t, nodeDef)

			// 验证基本属性
			assert.Equal(t, tt.plugin.Name(), nodeDef.PluginName())
			assert.Equal(t, tt.manifest.Name, nodeDef.Name())
			assert.Equal(t, tt.manifest.Author, nodeDef.Author())
			assert.Equal(t, tt.manifest.Description, nodeDef.Description())
			assert.Equal(t, tt.manifest.Icon, nodeDef.Icon())
			assert.Equal(t, tt.manifest.Type, nodeDef.Type())
			assert.Equal(t, tt.manifest.Version, nodeDef.Version().String())
			assert.Equal(t, tt.manifest.Category, nodeDef.Category())
			assert.Equal(t, tt.manifest.InputParams, nodeDef.InputParams())
			assert.Equal(t, tt.manifest.OutputParams, nodeDef.OutputParams())
			assert.Equal(t, tt.manifest.InputPorts, nodeDef.InputPorts())
			assert.Equal(t, tt.manifest.OutputPorts, nodeDef.OutputPorts())
			assert.Equal(t, tt.manifest.Exception, nodeDef.Exception())
			assert.Equal(t, tt.path, nodeDef.Path())
			assert.Equal(t, tt.plugin.Builtin(), nodeDef.Builtin())
			assert.True(t, nodeDef.Enabled()) // 默认启用
			assert.NotEmpty(t, nodeDef.ID())
		})
	}
}

func TestNodeDefinition_Activate(t *testing.T) {
	nodeDef, err := NewNodeDefinition(
		"test-id", time.Now(), time.Now(),
		"test-plugin", "Test Node", "author", "desc", "icon",
		"processor", "1.0.0", "data",
		[]*NodeParam{}, []*NodeParam{},
		[]*NodePort{}, []*NodePort{},
		false, "/path", false, false, // enabled = false
	)
	require.NoError(t, err)
	require.NotNil(t, nodeDef)

	// 验证初始状态
	assert.False(t, nodeDef.Enabled())

	// 激活节点定义
	nodeDef.Activate()

	// 验证激活后的状态
	assert.True(t, nodeDef.Enabled())
}

func TestNodeDefinition_Getters(t *testing.T) {
	id := "test-node-id"
	createdAt := time.Now().Add(-time.Hour)
	updatedAt := time.Now()
	pluginName := "test-plugin"
	name := "Test Node"
	author := "test-author"
	description := "A comprehensive test node"
	icon := "test-icon.png"
	nodeType := "transformer"
	version := "2.1.0"
	category := "processing"
	path := "/path/to/node"
	builtin := false
	enabled := true
	exception := true

	inputParams := []*NodeParam{
		{
			Name:        "param1",
			Type:        "number",
			ViewType:    "slider",
			Label:       "Parameter 1",
			Description: "First parameter",
			Placeholder: "Enter number",
			Required:    true,
			Default:     "10",
			ViewConfig:  map[string]interface{}{"min": 0, "max": 100},
		},
	}
	outputParams := []*NodeParam{
		{
			Name:        "result",
			Type:        "string",
			ViewType:    "text",
			Label:       "Result",
			Description: "Processing result",
			Required:    false,
		},
	}
	inputPorts := []*NodePort{
		{
			Name:     "input_data",
			Type:     "stream",
			Position: "left",
		},
	}
	outputPorts := []*NodePort{
		{
			Name:     "output_data",
			Type:     "stream",
			Position: "right",
		},
	}

	nodeDef, err := NewNodeDefinition(
		id, createdAt, updatedAt, pluginName, name, author, description, icon,
		nodeType, version, category, inputParams, outputParams, inputPorts, outputPorts,
		exception, path, builtin, enabled,
	)
	require.NoError(t, err)
	require.NotNil(t, nodeDef)

	// 测试所有 getter 方法
	assert.Equal(t, id, nodeDef.ID())
	assert.Equal(t, createdAt, nodeDef.CreatedAt())
	assert.Equal(t, updatedAt, nodeDef.UpdatedAt())
	assert.Equal(t, pluginName, nodeDef.PluginName())
	assert.Equal(t, name, nodeDef.Name())
	assert.Equal(t, author, nodeDef.Author())
	assert.Equal(t, description, nodeDef.Description())
	assert.Equal(t, icon, nodeDef.Icon())
	assert.Equal(t, nodeType, nodeDef.Type())
	assert.Equal(t, version, nodeDef.Version().String())
	assert.Equal(t, category, nodeDef.Category())
	assert.Equal(t, inputParams, nodeDef.InputParams())
	assert.Equal(t, outputParams, nodeDef.OutputParams())
	assert.Equal(t, inputPorts, nodeDef.InputPorts())
	assert.Equal(t, outputPorts, nodeDef.OutputPorts())
	assert.Equal(t, exception, nodeDef.Exception())
	assert.Equal(t, path, nodeDef.Path())
	assert.Equal(t, builtin, nodeDef.Builtin())
	assert.Equal(t, enabled, nodeDef.Enabled())
}
