package domain

import (
	"strconv"
	"strings"
)

const (
	// InstallationStrategyInstall 只安装不指定为最新
	InstallationStrategyInstall = iota
	// InstallationStrategyUpdate 安装并指定为最新
	InstallationStrategyUpdate
	// InstallationStrategySkip 跳过安装
	InstallationStrategySkip
)

type NodeInstallationStrategy struct {
	NodeDefinition *NodeDefinition
	Strategy       int
}

// CompareVersions 比较版本大小 返回 -1(小于), 0(等于), 1(大于)
func CompareVersions(v1, v2 string) int {
	parts1 := strings.Split(v1, ".")
	parts2 := strings.Split(v2, ".")

	maxLen := len(parts1)
	if len(parts2) > maxLen {
		maxLen = len(parts2)
	}

	for i := len(parts1); i < maxLen; i++ {
		parts1 = append(parts1, "0")
	}
	for i := len(parts2); i < maxLen; i++ {
		parts2 = append(parts2, "0")
	}

	for i := 0; i < maxLen; i++ {
		num1, err1 := strconv.Atoi(parts1[i])
		num2, err2 := strconv.Atoi(parts2[i])

		if err1 != nil || err2 != nil {
			if parts1[i] < parts2[i] {
				return -1
			} else if parts1[i] > parts2[i] {
				return 1
			}
			continue
		}

		if num1 < num2 {
			return -1
		} else if num1 > num2 {
			return 1
		}
	}

	return 0
}
