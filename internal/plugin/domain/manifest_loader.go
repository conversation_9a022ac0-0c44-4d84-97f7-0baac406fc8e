package domain

type PluginManifest struct {
	Name        string `json:"name" yaml:"name"`
	Version     string `json:"giveVersion" yaml:"giveVersion"`
	Author      string `json:"author" yaml:"author"`
	DisplayName string `json:"display_name" yaml:"display_name"`
	Description string `json:"description" yaml:"description"`
	Icon        string `json:"icon" yaml:"icon"`
}

type NodeDefinitionManifest struct {
	Name         string       `json:"name" yaml:"name"`
	Author       string       `json:"author" yaml:"author"`
	Version      string       `json:"giveVersion" yaml:"giveVersion"`
	Description  string       `json:"description" yaml:"description"`
	Icon         string       `json:"icon" yaml:"icon"`
	Type         string       `json:"type" yaml:"type"`
	Category     string       `json:"category" yaml:"category"`
	InputParams  []*NodeParam `json:"input_params" yaml:"input_params"`
	OutputParams []*NodeParam `json:"output_params" yaml:"output_params"`
	InputPorts   []*NodePort  `json:"input_ports" yaml:"input_ports"`
	OutputPorts  []*NodePort  `json:"output_ports" yaml:"output_ports"`
	Exception    bool         `json:"exception" yaml:"exception"`
}

type ManifestLoader interface {
	LoadPlugin(path string) (*PluginManifest, error)
	LoadNode(path string) (*NodeDefinitionManifest, error)
}
