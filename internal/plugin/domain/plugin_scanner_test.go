package domain

import (
	"os"
	"path/filepath"
	"resflow/internal/common/domain"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestPluginScanner_ScanPluginDirectory_Success(t *testing.T) {
	givePluginPath := t.TempDir()
	giveNodePath := filepath.Join(givePluginPath, "nodes", "test-node")
	err := os.<PERSON>ll(giveNodePath, 0755)
	assert.NoError(t, err)

	manifestLoader := &MoqManifestLoader{
		LoadPluginFunc: func(path string) (*PluginManifest, error) {
			return &PluginManifest{
				Name:        "test-plugin",
				Version:     "1.0.0",
				Author:      "test-author",
				DisplayName: "Test Plugin",
				Description: "A test plugin",
				Icon:        "test-icon.png",
			}, nil
		},
		LoadNodeFunc: func(path string) (*NodeDefinitionManifest, error) {
			return &NodeDefinitionManifest{
				Name:         "test-node",
				Author:       "test-author",
				Version:      "1.0.0",
				Description:  "A test node",
				Icon:         "test-icon.png",
				Type:         "test-type",
				Category:     "test-category",
				Exception:    false,
				InputParams:  []*NodeParam{},
				OutputParams: []*NodeParam{},
				InputPorts:   []*NodePort{},
				OutputPorts:  []*NodePort{},
			}, nil
		},
	}

	wantPlugin := &Plugin{
		name: "test-plugin",
		version: &domain.Version{
			Major: 1,
			Minor: 0,
			Patch: 0,
		},
		author:      "test-author",
		displayName: "Test Plugin",
		description: "A test plugin",
		icon:        "test-icon.png",
		path:        givePluginPath,
		builtin:     false,
		enabled:     true,
		nodeDefinitions: []*NodeDefinition{
			{
				pluginName:   "test-plugin",
				name:         "test-node",
				author:       "test-author",
				description:  "A test node",
				icon:         "test-icon.png",
				_type:        "test-type",
				version:      &domain.Version{Major: 1, Minor: 0, Patch: 0},
				category:     "test-category",
				inputParams:  []*NodeParam{},
				outputParams: []*NodeParam{},
				inputPorts:   []*NodePort{},
				outputPorts:  []*NodePort{},
				exception:    false,
				path:         giveNodePath,
				builtin:      false,
				enabled:      true,
			},
		},
	}

	scanner := NewPluginScanner(manifestLoader)
	gotPlugin, err := scanner.ScanPluginDirectory(givePluginPath, false)
	assert.NoError(t, err)
	assert.Equal(t, wantPlugin.Name(), gotPlugin.Name())
	assert.Equal(t, wantPlugin.Version().String(), gotPlugin.Version().String())
	assert.Equal(t, wantPlugin.Author(), gotPlugin.Author())
	assert.Equal(t, wantPlugin.DisplayName(), gotPlugin.DisplayName())
	assert.Equal(t, wantPlugin.Description(), gotPlugin.Description())
	assert.Equal(t, wantPlugin.Icon(), gotPlugin.Icon())
	assert.Equal(t, wantPlugin.Path(), gotPlugin.Path())
	assert.Equal(t, wantPlugin.Builtin(), gotPlugin.Builtin())
	assert.Equal(t, wantPlugin.Enabled(), gotPlugin.Enabled())
	assert.Equal(t, len(wantPlugin.NodeDefinitions()), len(gotPlugin.NodeDefinitions()))

	wantNodeDefinitions := wantPlugin.NodeDefinitions()
	gotNodeDefinitions := gotPlugin.NodeDefinitions()
	assert.Equal(t, wantNodeDefinitions[0].Name(), gotNodeDefinitions[0].Name())
	assert.Equal(t, wantNodeDefinitions[0].PluginName(), gotNodeDefinitions[0].PluginName())
	assert.Equal(t, wantNodeDefinitions[0].Author(), gotNodeDefinitions[0].Author())
	assert.Equal(t, wantNodeDefinitions[0].Description(), gotNodeDefinitions[0].Description())
	assert.Equal(t, wantNodeDefinitions[0].Icon(), gotNodeDefinitions[0].Icon())
	assert.Equal(t, wantNodeDefinitions[0].Type(), gotNodeDefinitions[0].Type())
	assert.Equal(t, wantNodeDefinitions[0].Version().String(), gotNodeDefinitions[0].Version().String())
	assert.Equal(t, wantNodeDefinitions[0].Category(), gotNodeDefinitions[0].Category())
	assert.Equal(t, wantNodeDefinitions[0].InputParams(), gotNodeDefinitions[0].InputParams())
	assert.Equal(t, wantNodeDefinitions[0].OutputParams(), gotNodeDefinitions[0].OutputParams())
	assert.Equal(t, wantNodeDefinitions[0].InputPorts(), gotNodeDefinitions[0].InputPorts())
	assert.Equal(t, wantNodeDefinitions[0].OutputPorts(), gotNodeDefinitions[0].OutputPorts())
	assert.Equal(t, wantNodeDefinitions[0].Exception(), gotNodeDefinitions[0].Exception())
	assert.Equal(t, wantNodeDefinitions[0].Path(), gotNodeDefinitions[0].Path())
	assert.Equal(t, wantNodeDefinitions[0].Builtin(), gotNodeDefinitions[0].Builtin())
	assert.Equal(t, wantNodeDefinitions[0].Enabled(), gotNodeDefinitions[0].Enabled())
}
