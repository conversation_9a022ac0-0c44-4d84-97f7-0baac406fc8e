package domain

import (
	"context"
)

type NodeDefinitionInstaller struct {
	repository NodeDefinitionRepository
}

func NewNodeDefinitionInstaller(repository NodeDefinitionRepository) *NodeDefinitionInstaller {
	return &NodeDefinitionInstaller{repository: repository}
}

func (ndi NodeDefinitionInstaller) Install(ctx context.Context, plugin *Plugin, nodeDefinitions []*NodeDefinition) ([]*NodeDefinition, error) {
	installedNodeDefinitions := make([]*NodeDefinition, 0)
	for _, nodeDefinition := range nodeDefinitions {
		found, err := ndi.repository.FindByTypeAndVersion(ctx, plugin.Name(), nodeDefinition.Type(), nodeDefinition.Version())
		if err != nil {
			return nil, err
		}
		latest, err := ndi.repository.FindLatestByType(ctx, plugin.Name(), nodeDefinition.Type())
		if err != nil {
			return nil, err
		}

		if found != nil {
			continue
		}

		if latest != nil {
			if CompareVersions(nodeDefinition.Version(), latest.Version()) > 0 {
				err = ndi.repository.DisableByType(ctx, plugin.Name(), nodeDefinition.Type())
				if err != nil {
					return nil, err
				}
				nodeDefinition.Activate()
			}
		} else {
			nodeDefinition.Activate()
		}

		installedNodeDefinitions = append(installedNodeDefinitions, nodeDefinition)
		err = ndi.repository.Save(ctx, nodeDefinition)
		if err != nil {
			return nil, err
		}
	}

	return installedNodeDefinitions, nil
}
