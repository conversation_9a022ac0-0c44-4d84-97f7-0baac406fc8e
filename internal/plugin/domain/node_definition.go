package domain

import (
	"errors"
	"resflow/internal/common/domain"
	"time"

	"github.com/google/uuid"
)

type NodeDefinition struct {
	domain.AggregateRoot
	pluginName   string
	name         string
	author       string
	description  string
	icon         string
	_type        string
	version      string
	category     string
	inputParams  []*NodeParam
	outputParams []*NodeParam
	inputPorts   []*NodePort
	outputPorts  []*NodePort
	exception    bool
	path         string
	builtin      bool
	enabled      bool
}

func NewNodeDefinition(id string, createdAt, updatedAt time.Time, pluginName, name, author, description, icon, _type, version, category string, inputParams, outputParams []*NodeParam, inputPorts, outputPorts []*NodePort, exception bool, path string, builtin, enabled bool,
) *NodeDefinition {
	return &NodeDefinition{
		AggregateRoot: domain.NewAggregateRoot(id, createdAt, updatedAt),
		pluginName:    pluginName,
		name:          name,
		author:        author,
		description:   description,
		icon:          icon,
		_type:         _type,
		version:       version,
		category:      category,
		inputParams:   inputParams,
		outputParams:  outputParams,
		inputPorts:    inputPorts,
		outputPorts:   outputPorts,
		exception:     exception,
		path:          path,
		builtin:       builtin,
		enabled:       enabled,
	}
}

func NewNodeDefinitionFromManifest(plugin *Plugin, manifest *NodeDefinitionManifest, path string) (*NodeDefinition, error) {
	if manifest.Type == "" || manifest.Version == "" {
		return nil, errors.New("node type and giveVersion must be set")
	}
	return &NodeDefinition{
		AggregateRoot: domain.NewAggregateRoot(uuid.New().String(), time.Now(), time.Now()),
		pluginName:    plugin.Name(),
		name:          manifest.Name,
		author:        manifest.Author,
		description:   manifest.Description,
		icon:          manifest.Icon,
		_type:         manifest.Type,
		version:       manifest.Version,
		category:      manifest.Category,
		inputParams:   manifest.InputParams,
		outputParams:  manifest.OutputParams,
		inputPorts:    manifest.InputPorts,
		outputPorts:   manifest.OutputPorts,
		exception:     manifest.Exception,
		path:          path,
		builtin:       plugin.Builtin(),
		enabled:       true,
	}, nil
}

func (def NodeDefinition) Name() string {
	return def.name
}

func (def NodeDefinition) PluginName() string {
	return def.pluginName
}

func (def NodeDefinition) Author() string {
	return def.author
}

func (def NodeDefinition) Description() string {
	return def.description
}

func (def NodeDefinition) Icon() string {
	return def.icon
}

func (def NodeDefinition) Type() string {
	return def._type
}

func (def NodeDefinition) Version() string {
	return def.version
}

func (def NodeDefinition) Category() string {
	return def.category
}

func (def NodeDefinition) InputParams() []*NodeParam {
	return def.inputParams
}

func (def NodeDefinition) OutputParams() []*NodeParam {
	return def.outputParams
}

func (def NodeDefinition) InputPorts() []*NodePort {
	return def.inputPorts
}

func (def NodeDefinition) OutputPorts() []*NodePort {
	return def.outputPorts
}

func (def NodeDefinition) Exception() bool {
	return def.exception
}

func (def NodeDefinition) Path() string {
	return def.path
}

func (def NodeDefinition) Builtin() bool {
	return def.builtin
}

func (def NodeDefinition) Enabled() bool {
	return def.enabled
}

func (def NodeDefinition) Activate() {
	def.enabled = true
}

type NodeParam struct {
	Name        string
	Type        string
	ViewType    string
	Label       string
	Description string
	Placeholder string
	Required    bool
	Default     string
	ViewConfig  map[string]interface{}
}

type NodePort struct {
	Name     string
	Type     string
	Position string
}
