package domain

import (
	"resflow/internal/common/domain"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewPluginFromManifest(t *testing.T) {
	tests := []struct {
		name     string
		manifest *PluginManifest
		path     string
		builtin  bool
		want     *Plugin
		wantErr  error
	}{
		{
			name: "成功创建插件",
			manifest: &PluginManifest{
				Name:        "test-plugin",
				Version:     "1.0.0",
				Author:      "test-author",
				DisplayName: "Test Plugin",
				Description: "A test plugin",
				Icon:        "test-icon.png",
			},
			path:    "/path/to/plugin",
			builtin: false,
			want: &Plugin{
				name:        "test-plugin",
				version:     &domain.Version{Major: 1, Minor: 0, Patch: 0},
				author:      "test-author",
				displayName: "Test Plugin",
				description: "A test plugin",
				icon:        "test-icon.png",
				path:        "/path/to/plugin",
				builtin:     false,
				enabled:     true,
			},
			wantErr: nil,
		},
		{
			name: "插件名称为空",
			manifest: &PluginManifest{
				Name:    "",
				Version: "1.0.0",
			},
			path:    "/path/to/plugin",
			builtin: false,
			want:    nil,
			wantErr: ErrPluginNameAndVersionRequired,
		},
		{
			name: "插件版本为空",
			manifest: &PluginManifest{
				Name:    "test-plugin",
				Version: "",
			},
			path:    "/path/to/plugin",
			builtin: false,
			want:    nil,
			wantErr: ErrPluginNameAndVersionRequired,
		},
		{
			name: "版本格式错误",
			manifest: &PluginManifest{
				Name:    "test-plugin",
				Version: "invalid-giveVersion",
			},
			path:    "/path/to/plugin",
			builtin: false,
			want:    nil,
			wantErr: ErrInvalidPluginVersionFormat,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			plugin, err := NewPluginFromManifest(tt.manifest, tt.path, tt.builtin)

			assert.Equal(t, tt.wantErr, err)

			if tt.want != nil {
				assert.Equal(t, tt.want.name, plugin.Name())
				assert.Equal(t, tt.want.version, plugin.Version())
				assert.Equal(t, tt.want.author, plugin.Author())
				assert.Equal(t, tt.want.displayName, plugin.DisplayName())
				assert.Equal(t, tt.want.description, plugin.Description())
				assert.Equal(t, tt.want.icon, plugin.Icon())
				assert.Equal(t, tt.want.path, plugin.Path())
				assert.Equal(t, tt.want.builtin, plugin.Builtin())
				assert.Equal(t, tt.want.enabled, plugin.Enabled())
				assert.Empty(t, plugin.NodeDefinitions())
			}
		})
	}
}

func TestNewPlugin(t *testing.T) {
	id := "test-id"
	createdAt := time.Now().Add(-time.Hour)
	updatedAt := time.Now()
	name := "test-plugin"
	author := "test-author"
	displayName := "Test Plugin"
	description := "A test plugin"
	icon := "test-icon.png"
	path := "/path/to/plugin"
	builtin := true
	enabled := false

	tests := []struct {
		name        string
		giveVersion string
		wantErr     error
	}{
		{
			name:        "成功创建插件",
			giveVersion: "1.0.0",
			wantErr:     nil,
		},
		{
			name:        "版本格式错误",
			giveVersion: "invalid-giveVersion",
			wantErr:     ErrInvalidPluginVersionFormat,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			plugin, err := NewPlugin(id, createdAt, updatedAt, name, tt.giveVersion, author, displayName, description, icon, path, builtin, enabled)

			assert.Equal(t, tt.wantErr, err)

			if tt.wantErr == nil {
				assert.Equal(t, id, plugin.ID())
				assert.Equal(t, createdAt, plugin.CreatedAt())
				assert.Equal(t, updatedAt, plugin.UpdatedAt())
				assert.Equal(t, name, plugin.Name())
				assert.Equal(t, tt.giveVersion, plugin.Version().String())
				assert.Equal(t, author, plugin.Author())
				assert.Equal(t, displayName, plugin.DisplayName())
				assert.Equal(t, description, plugin.Description())
				assert.Equal(t, icon, plugin.Icon())
				assert.Equal(t, path, plugin.Path())
				assert.Equal(t, builtin, plugin.Builtin())
				assert.Equal(t, enabled, plugin.Enabled())
				assert.Empty(t, plugin.NodeDefinitions())
			}
		})
	}
}

func TestPlugin_Enable(t *testing.T) {
	manifest := &PluginManifest{
		Name:    "test-plugin",
		Version: "1.0.0",
	}
	plugin, err := NewPluginFromManifest(manifest, "/path", false)
	require.NoError(t, err)

	// 初始状态应该是启用的
	assert.True(t, plugin.Enabled())

	// 手动设置为禁用状态（通过创建禁用的插件）
	disabledPlugin, err := NewPlugin("test-id", time.Now(), time.Now(), "test", "1.0.0", "author", "display", "desc", "icon", "/path", false, false)
	require.NoError(t, err)
	assert.False(t, disabledPlugin.Enabled())

	// 启用插件
	disabledPlugin.Enable()
	assert.True(t, disabledPlugin.Enabled())
}

func TestPlugin_NodeDefinitions(t *testing.T) {
	manifest := &PluginManifest{
		Name:    "test-plugin",
		Version: "1.0.0",
	}
	plugin, err := NewPluginFromManifest(manifest, "/path", false)
	require.NoError(t, err)

	// 初始状态应该为空
	nodeDefinitions := plugin.NodeDefinitions()
	assert.Empty(t, nodeDefinitions)

	// 验证返回的是副本，修改不会影响原始数据
	nodeDefinitions = append(nodeDefinitions, &NodeDefinition{})
	assert.Empty(t, plugin.NodeDefinitions())
}

func TestPlugin_AddNodeDefinition(t *testing.T) {
	manifest := &PluginManifest{
		Name:    "test-plugin",
		Version: "1.0.0",
	}
	plugin, err := NewPluginFromManifest(manifest, "/path", false)
	require.NoError(t, err)

	// 创建测试节点定义
	nodeDef1, err := NewNodeDefinition(
		"node1", time.Now(), time.Now(),
		"test-plugin", "Node 1", "author", "desc", "icon",
		"type1", "1.0.0", "category",
		[]*NodeParam{}, []*NodeParam{},
		[]*NodePort{}, []*NodePort{},
		false, "/path", false, true,
	)
	assert.NoError(t, err, nil)

	nodeDef2, err := NewNodeDefinition(
		"node2", time.Now(), time.Now(),
		"test-plugin", "Node 2", "author", "desc", "icon",
		"type2", "1.0.0", "category",
		[]*NodeParam{}, []*NodeParam{},
		[]*NodePort{}, []*NodePort{},
		false, "/path", false, true,
	)
	assert.NoError(t, err, nil)

	// 相同类型和版本的节点定义
	nodeDef3, err := NewNodeDefinition(
		"node3", time.Now(), time.Now(),
		"test-plugin", "Node 3", "author", "desc", "icon",
		"type1", "1.0.0", "category", // 与 nodeDef1 相同的 type 和 giveVersion
		[]*NodeParam{}, []*NodeParam{},
		[]*NodePort{}, []*NodePort{},
		false, "/path", false, true,
	)
	assert.NoError(t, err, nil)

	tests := []struct {
		name    string
		nodeDef *NodeDefinition
		wantErr error
	}{
		{
			name:    "成功添加第一个节点定义",
			nodeDef: nodeDef1,
			wantErr: nil,
		},
		{
			name:    "成功添加第二个节点定义",
			nodeDef: nodeDef2,
			wantErr: nil,
		},
		{
			name:    "添加重复的节点定义",
			nodeDef: nodeDef3,
			wantErr: ErrNodeDefinitionAlreadyExists,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := plugin.AddNodeDefinition(tt.nodeDef)

			assert.Equal(t, tt.wantErr, err)
		})
	}

	// 验证最终状态
	nodeDefinitions := plugin.NodeDefinitions()
	assert.Len(t, nodeDefinitions, 2) // 只有两个成功添加的节点定义
}

func TestPlugin_Getters(t *testing.T) {
	manifest := &PluginManifest{
		Name:        "test-plugin",
		Version:     "1.2.3",
		Author:      "test-author",
		DisplayName: "Test Plugin",
		Description: "A test plugin for testing",
		Icon:        "test-icon.png",
	}
	path := "/path/to/plugin"

	plugin, err := NewPluginFromManifest(manifest, path, true)
	require.NoError(t, err)

	// 测试所有 getter 方法
	assert.Equal(t, manifest.Name, plugin.Name())
	assert.Equal(t, manifest.Version, plugin.Version().String())
	assert.Equal(t, manifest.Author, plugin.Author())
	assert.Equal(t, manifest.DisplayName, plugin.DisplayName())
	assert.Equal(t, manifest.Description, plugin.Description())
	assert.Equal(t, manifest.Icon, plugin.Icon())
	assert.Equal(t, path, plugin.Path())
	assert.Equal(t, true, plugin.Builtin())
	assert.True(t, plugin.Enabled())
}

func TestPlugin_Version(t *testing.T) {
	manifest := &PluginManifest{
		Name:    "test-plugin",
		Version: "2.1.0",
	}
	plugin, err := NewPluginFromManifest(manifest, "/path", false)
	require.NoError(t, err)

	version := plugin.Version()
	require.NotNil(t, version)
	assert.Equal(t, 2, version.Major)
	assert.Equal(t, 1, version.Minor)
	assert.Equal(t, 0, version.Patch)
	assert.Equal(t, "2.1.0", version.String())
}
