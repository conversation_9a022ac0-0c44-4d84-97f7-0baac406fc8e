package domain

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestPluginManager_Install(t *testing.T) {
	mockRepo := &MoqPluginRepository{
		FindByNameAndVersionFunc: func(ctx context.Context, name string, version string) (*Plugin, error) {
			return nil, nil
		},
		SaveFunc: func(ctx context.Context, plugin *Plugin) error {
			return nil
		},
	}
	pm := NewPluginManager(mockRepo)
	err := pm.Install(context.Background(), &Plugin{})
	assert.NoError(t, err)
}
