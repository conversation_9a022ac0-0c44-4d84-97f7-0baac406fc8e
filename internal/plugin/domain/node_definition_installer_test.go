package domain

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestNodeDefinitionInstaller_Install_Success(t *testing.T) {
	nodeDefinition, err := NewNodeDefinition("1", time.Now(), time.Now(), "test", "test", "test", "test", "test", "test", "0.0.1", "test", []*NodeParam{}, []*NodeParam{}, []*NodePort{}, []*NodePort{}, false, "test", false, false)
	assert.NoError(t, err, nil)
	giveNodeDefinitions := []*NodeDefinition{
		nodeDefinition,
	}

	mockRepo := &MoqNodeDefinitionRepository{
		FindByTypeAndVersionFunc: func(ctx context.Context, pluginName string, typeVar string, version string) (*NodeDefinition, error) {
			return nil, nil
		},
		FindLatestByTypeFunc: func(ctx context.Context, pluginName string, typeVar string) (*NodeDefinition, error) {
			return nil, nil
		},
		SaveFunc: func(ctx context.Context, nodeDefinition *NodeDefinition) error {
			return nil
		},
	}

	ndi := NewNodeDefinitionInstaller(mockRepo)
	_, err = ndi.Install(context.Background(), &Plugin{}, giveNodeDefinitions)
	assert.NoError(t, err)
}
