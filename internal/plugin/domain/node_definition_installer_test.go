package domain

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNodeDefinitionInstaller_Install_NewNodeDefinition(t *testing.T) {
	// 创建测试插件
	plugin, err := NewPluginFromManifest(&PluginManifest{
		Name:    "test-plugin",
		Version: "1.0.0",
	}, "/test/path", false)
	require.NoError(t, err)

	// 创建测试节点定义
	nodeDefinition, err := NewNodeDefinition(
		"1", time.Now(), time.Now(),
		"test-plugin", "test-node", "test-author", "test-desc", "test-icon",
		"processor", "1.0.0", "data",
		[]*NodeParam{}, []*NodeParam{}, []*NodePort{}, []*NodePort{},
		false, "/test/path", false, false,
	)
	require.NoError(t, err)

	giveNodeDefinitions := []*NodeDefinition{nodeDefinition}

	mockRepo := &MoqNodeDefinitionRepository{
		FindByTypeAndVersionFunc: func(ctx context.Context, pluginName string, typeVar string, version string) (*NodeDefinition, error) {
			return nil, nil // 没有找到现有的节点定义
		},
		FindLatestByTypeFunc: func(ctx context.Context, pluginName string, typeVar string) (*NodeDefinition, error) {
			return nil, nil // 没有找到最新的节点定义
		},
		SaveFunc: func(ctx context.Context, nodeDefinition *NodeDefinition) error {
			return nil
		},
	}

	ndi := NewNodeDefinitionInstaller(mockRepo)
	installedNodes, err := ndi.Install(context.Background(), plugin, giveNodeDefinitions)

	require.NoError(t, err)
	assert.Len(t, installedNodes, 1)
	assert.True(t, installedNodes[0].Enabled()) // 应该被激活

	// 验证调用次数
	assert.Len(t, mockRepo.FindByTypeAndVersionCalls(), 1)
	assert.Len(t, mockRepo.FindLatestByTypeCalls(), 1)
	assert.Len(t, mockRepo.SaveCalls(), 1)
}

func TestNodeDefinitionInstaller_Install_ExistingNodeDefinition(t *testing.T) {
	// 创建测试插件
	plugin, err := NewPluginFromManifest(&PluginManifest{
		Name:    "test-plugin",
		Version: "1.0.0",
	}, "/test/path", false)
	require.NoError(t, err)

	// 创建测试节点定义
	nodeDefinition, err := NewNodeDefinition(
		"1", time.Now(), time.Now(),
		"test-plugin", "test-node", "test-author", "test-desc", "test-icon",
		"processor", "1.0.0", "data",
		[]*NodeParam{}, []*NodeParam{}, []*NodePort{}, []*NodePort{},
		false, "/test/path", false, false,
	)
	require.NoError(t, err)

	// 创建已存在的节点定义
	existingNodeDefinition, err := NewNodeDefinition(
		"2", time.Now(), time.Now(),
		"test-plugin", "existing-node", "test-author", "test-desc", "test-icon",
		"processor", "1.0.0", "data",
		[]*NodeParam{}, []*NodeParam{}, []*NodePort{}, []*NodePort{},
		false, "/test/path", false, true,
	)
	require.NoError(t, err)

	giveNodeDefinitions := []*NodeDefinition{nodeDefinition}

	mockRepo := &MoqNodeDefinitionRepository{
		FindByTypeAndVersionFunc: func(ctx context.Context, pluginName string, typeVar string, version string) (*NodeDefinition, error) {
			return existingNodeDefinition, nil // 找到现有的节点定义
		},
		FindLatestByTypeFunc: func(ctx context.Context, pluginName string, typeVar string) (*NodeDefinition, error) {
			return nil, nil
		},
		SaveFunc: func(ctx context.Context, nodeDefinition *NodeDefinition) error {
			return nil
		},
	}

	ndi := NewNodeDefinitionInstaller(mockRepo)
	installedNodes, err := ndi.Install(context.Background(), plugin, giveNodeDefinitions)

	require.NoError(t, err)
	assert.Empty(t, installedNodes) // 没有安装任何节点，因为已存在

	// 验证调用次数
	assert.Len(t, mockRepo.FindByTypeAndVersionCalls(), 1)
	assert.Len(t, mockRepo.FindLatestByTypeCalls(), 1)
	assert.Len(t, mockRepo.SaveCalls(), 0) // 不应该调用 Save
}

func TestNodeDefinitionInstaller_Install_UpgradeVersion(t *testing.T) {
	// 创建测试插件
	plugin, err := NewPluginFromManifest(&PluginManifest{
		Name:    "test-plugin",
		Version: "1.0.0",
	}, "/test/path", false)
	require.NoError(t, err)

	// 创建新版本的节点定义
	newNodeDefinition, err := NewNodeDefinition(
		"1", time.Now(), time.Now(),
		"test-plugin", "test-node", "test-author", "test-desc", "test-icon",
		"processor", "2.0.0", "data", // 新版本 2.0.0
		[]*NodeParam{}, []*NodeParam{}, []*NodePort{}, []*NodePort{},
		false, "/test/path", false, false,
	)
	require.NoError(t, err)

	// 创建旧版本的节点定义
	oldNodeDefinition, err := NewNodeDefinition(
		"2", time.Now(), time.Now(),
		"test-plugin", "old-node", "test-author", "test-desc", "test-icon",
		"processor", "1.0.0", "data", // 旧版本 1.0.0
		[]*NodeParam{}, []*NodeParam{}, []*NodePort{}, []*NodePort{},
		false, "/test/path", false, true,
	)
	require.NoError(t, err)

	giveNodeDefinitions := []*NodeDefinition{newNodeDefinition}

	mockRepo := &MoqNodeDefinitionRepository{
		FindByTypeAndVersionFunc: func(ctx context.Context, pluginName string, typeVar string, version string) (*NodeDefinition, error) {
			return nil, nil // 没有找到相同版本的节点定义
		},
		FindLatestByTypeFunc: func(ctx context.Context, pluginName string, typeVar string) (*NodeDefinition, error) {
			return oldNodeDefinition, nil // 找到旧版本的节点定义
		},
		DisableByTypeFunc: func(ctx context.Context, pluginName string, typeVar string) error {
			return nil
		},
		SaveFunc: func(ctx context.Context, nodeDefinition *NodeDefinition) error {
			return nil
		},
	}

	ndi := NewNodeDefinitionInstaller(mockRepo)
	installedNodes, err := ndi.Install(context.Background(), plugin, giveNodeDefinitions)

	require.NoError(t, err)
	assert.Len(t, installedNodes, 1)
	assert.True(t, installedNodes[0].Enabled()) // 新版本应该被激活

	// 验证调用次数
	assert.Len(t, mockRepo.FindByTypeAndVersionCalls(), 1)
	assert.Len(t, mockRepo.FindLatestByTypeCalls(), 1)
	assert.Len(t, mockRepo.DisableByTypeCalls(), 1) // 应该禁用旧版本
	assert.Len(t, mockRepo.SaveCalls(), 1)
}

func TestNodeDefinitionInstaller_Install_DowngradeVersion(t *testing.T) {
	// 创建测试插件
	plugin, err := NewPluginFromManifest(&PluginManifest{
		Name:    "test-plugin",
		Version: "1.0.0",
	}, "/test/path", false)
	require.NoError(t, err)

	// 创建旧版本的节点定义
	oldNodeDefinition, err := NewNodeDefinition(
		"1", time.Now(), time.Now(),
		"test-plugin", "test-node", "test-author", "test-desc", "test-icon",
		"processor", "1.0.0", "data", // 旧版本 1.0.0
		[]*NodeParam{}, []*NodeParam{}, []*NodePort{}, []*NodePort{},
		false, "/test/path", false, false,
	)
	require.NoError(t, err)

	// 创建新版本的节点定义
	newNodeDefinition, err := NewNodeDefinition(
		"2", time.Now(), time.Now(),
		"test-plugin", "new-node", "test-author", "test-desc", "test-icon",
		"processor", "2.0.0", "data", // 新版本 2.0.0
		[]*NodeParam{}, []*NodeParam{}, []*NodePort{}, []*NodePort{},
		false, "/test/path", false, true,
	)
	require.NoError(t, err)

	giveNodeDefinitions := []*NodeDefinition{oldNodeDefinition}

	mockRepo := &MoqNodeDefinitionRepository{
		FindByTypeAndVersionFunc: func(ctx context.Context, pluginName string, typeVar string, version string) (*NodeDefinition, error) {
			return nil, nil // 没有找到相同版本的节点定义
		},
		FindLatestByTypeFunc: func(ctx context.Context, pluginName string, typeVar string) (*NodeDefinition, error) {
			return newNodeDefinition, nil // 找到更新版本的节点定义
		},
		SaveFunc: func(ctx context.Context, nodeDefinition *NodeDefinition) error {
			return nil
		},
	}

	ndi := NewNodeDefinitionInstaller(mockRepo)
	installedNodes, err := ndi.Install(context.Background(), plugin, giveNodeDefinitions)

	require.NoError(t, err)
	assert.Len(t, installedNodes, 1)
	assert.False(t, installedNodes[0].Enabled()) // 旧版本不应该被激活

	// 验证调用次数
	assert.Len(t, mockRepo.FindByTypeAndVersionCalls(), 1)
	assert.Len(t, mockRepo.FindLatestByTypeCalls(), 1)
	assert.Len(t, mockRepo.DisableByTypeCalls(), 0) // 不应该禁用任何版本
	assert.Len(t, mockRepo.SaveCalls(), 1)
}

func TestNodeDefinitionInstaller_Install_FindByTypeAndVersionError(t *testing.T) {
	// 创建测试插件
	plugin, err := NewPluginFromManifest(&PluginManifest{
		Name:    "test-plugin",
		Version: "1.0.0",
	}, "/test/path", false)
	require.NoError(t, err)

	// 创建测试节点定义
	nodeDefinition, err := NewNodeDefinition(
		"1", time.Now(), time.Now(),
		"test-plugin", "test-node", "test-author", "test-desc", "test-icon",
		"processor", "1.0.0", "data",
		[]*NodeParam{}, []*NodeParam{}, []*NodePort{}, []*NodePort{},
		false, "/test/path", false, false,
	)
	require.NoError(t, err)

	giveNodeDefinitions := []*NodeDefinition{nodeDefinition}
	expectedError := errors.New("database connection error")

	mockRepo := &MoqNodeDefinitionRepository{
		FindByTypeAndVersionFunc: func(ctx context.Context, pluginName string, typeVar string, version string) (*NodeDefinition, error) {
			return nil, expectedError
		},
	}

	ndi := NewNodeDefinitionInstaller(mockRepo)
	installedNodes, err := ndi.Install(context.Background(), plugin, giveNodeDefinitions)

	assert.Error(t, err)
	assert.Equal(t, expectedError, err)
	assert.Nil(t, installedNodes)
}

func TestNodeDefinitionInstaller_Install_FindLatestByTypeError(t *testing.T) {
	// 创建测试插件
	plugin, err := NewPluginFromManifest(&PluginManifest{
		Name:    "test-plugin",
		Version: "1.0.0",
	}, "/test/path", false)
	require.NoError(t, err)

	// 创建测试节点定义
	nodeDefinition, err := NewNodeDefinition(
		"1", time.Now(), time.Now(),
		"test-plugin", "test-node", "test-author", "test-desc", "test-icon",
		"processor", "1.0.0", "data",
		[]*NodeParam{}, []*NodeParam{}, []*NodePort{}, []*NodePort{},
		false, "/test/path", false, false,
	)
	require.NoError(t, err)

	giveNodeDefinitions := []*NodeDefinition{nodeDefinition}
	expectedError := errors.New("query error")

	mockRepo := &MoqNodeDefinitionRepository{
		FindByTypeAndVersionFunc: func(ctx context.Context, pluginName string, typeVar string, version string) (*NodeDefinition, error) {
			return nil, nil
		},
		FindLatestByTypeFunc: func(ctx context.Context, pluginName string, typeVar string) (*NodeDefinition, error) {
			return nil, expectedError
		},
	}

	ndi := NewNodeDefinitionInstaller(mockRepo)
	installedNodes, err := ndi.Install(context.Background(), plugin, giveNodeDefinitions)

	assert.Error(t, err)
	assert.Equal(t, expectedError, err)
	assert.Nil(t, installedNodes)
}

func TestNodeDefinitionInstaller_Install_DisableByTypeError(t *testing.T) {
	// 创建测试插件
	plugin, err := NewPluginFromManifest(&PluginManifest{
		Name:    "test-plugin",
		Version: "1.0.0",
	}, "/test/path", false)
	require.NoError(t, err)

	// 创建新版本的节点定义
	newNodeDefinition, err := NewNodeDefinition(
		"1", time.Now(), time.Now(),
		"test-plugin", "test-node", "test-author", "test-desc", "test-icon",
		"processor", "2.0.0", "data",
		[]*NodeParam{}, []*NodeParam{}, []*NodePort{}, []*NodePort{},
		false, "/test/path", false, false,
	)
	require.NoError(t, err)

	// 创建旧版本的节点定义
	oldNodeDefinition, err := NewNodeDefinition(
		"2", time.Now(), time.Now(),
		"test-plugin", "old-node", "test-author", "test-desc", "test-icon",
		"processor", "1.0.0", "data",
		[]*NodeParam{}, []*NodeParam{}, []*NodePort{}, []*NodePort{},
		false, "/test/path", false, true,
	)
	require.NoError(t, err)

	giveNodeDefinitions := []*NodeDefinition{newNodeDefinition}
	expectedError := errors.New("disable operation failed")

	mockRepo := &MoqNodeDefinitionRepository{
		FindByTypeAndVersionFunc: func(ctx context.Context, pluginName string, typeVar string, version string) (*NodeDefinition, error) {
			return nil, nil
		},
		FindLatestByTypeFunc: func(ctx context.Context, pluginName string, typeVar string) (*NodeDefinition, error) {
			return oldNodeDefinition, nil
		},
		DisableByTypeFunc: func(ctx context.Context, pluginName string, typeVar string) error {
			return expectedError
		},
	}

	ndi := NewNodeDefinitionInstaller(mockRepo)
	installedNodes, err := ndi.Install(context.Background(), plugin, giveNodeDefinitions)

	assert.Error(t, err)
	assert.Equal(t, expectedError, err)
	assert.Nil(t, installedNodes)
}

func TestNodeDefinitionInstaller_Install_SaveError(t *testing.T) {
	// 创建测试插件
	plugin, err := NewPluginFromManifest(&PluginManifest{
		Name:    "test-plugin",
		Version: "1.0.0",
	}, "/test/path", false)
	require.NoError(t, err)

	// 创建测试节点定义
	nodeDefinition, err := NewNodeDefinition(
		"1", time.Now(), time.Now(),
		"test-plugin", "test-node", "test-author", "test-desc", "test-icon",
		"processor", "1.0.0", "data",
		[]*NodeParam{}, []*NodeParam{}, []*NodePort{}, []*NodePort{},
		false, "/test/path", false, false,
	)
	require.NoError(t, err)

	giveNodeDefinitions := []*NodeDefinition{nodeDefinition}
	expectedError := errors.New("save operation failed")

	mockRepo := &MoqNodeDefinitionRepository{
		FindByTypeAndVersionFunc: func(ctx context.Context, pluginName string, typeVar string, version string) (*NodeDefinition, error) {
			return nil, nil
		},
		FindLatestByTypeFunc: func(ctx context.Context, pluginName string, typeVar string) (*NodeDefinition, error) {
			return nil, nil
		},
		SaveFunc: func(ctx context.Context, nodeDefinition *NodeDefinition) error {
			return expectedError
		},
	}

	ndi := NewNodeDefinitionInstaller(mockRepo)
	installedNodes, err := ndi.Install(context.Background(), plugin, giveNodeDefinitions)

	assert.Error(t, err)
	assert.Equal(t, expectedError, err)
	assert.Nil(t, installedNodes)
}

func TestNodeDefinitionInstaller_Install_MultipleNodeDefinitions(t *testing.T) {
	// 创建测试插件
	plugin, err := NewPluginFromManifest(&PluginManifest{
		Name:    "test-plugin",
		Version: "1.0.0",
	}, "/test/path", false)
	require.NoError(t, err)

	// 创建多个测试节点定义
	nodeDefinition1, err := NewNodeDefinition(
		"1", time.Now(), time.Now(),
		"test-plugin", "node1", "test-author", "test-desc", "test-icon",
		"processor", "1.0.0", "data",
		[]*NodeParam{}, []*NodeParam{}, []*NodePort{}, []*NodePort{},
		false, "/test/path", false, false,
	)
	require.NoError(t, err)

	nodeDefinition2, err := NewNodeDefinition(
		"2", time.Now(), time.Now(),
		"test-plugin", "node2", "test-author", "test-desc", "test-icon",
		"transformer", "1.0.0", "processing",
		[]*NodeParam{}, []*NodeParam{}, []*NodePort{}, []*NodePort{},
		false, "/test/path", false, false,
	)
	require.NoError(t, err)

	nodeDefinition3, err := NewNodeDefinition(
		"3", time.Now(), time.Now(),
		"test-plugin", "node3", "test-author", "test-desc", "test-icon",
		"output", "1.0.0", "io",
		[]*NodeParam{}, []*NodeParam{}, []*NodePort{}, []*NodePort{},
		false, "/test/path", false, false,
	)
	require.NoError(t, err)

	giveNodeDefinitions := []*NodeDefinition{nodeDefinition1, nodeDefinition2, nodeDefinition3}

	mockRepo := &MoqNodeDefinitionRepository{
		FindByTypeAndVersionFunc: func(ctx context.Context, pluginName string, typeVar string, version string) (*NodeDefinition, error) {
			return nil, nil // 所有节点都是新的
		},
		FindLatestByTypeFunc: func(ctx context.Context, pluginName string, typeVar string) (*NodeDefinition, error) {
			return nil, nil // 没有现有版本
		},
		SaveFunc: func(ctx context.Context, nodeDefinition *NodeDefinition) error {
			return nil
		},
	}

	ndi := NewNodeDefinitionInstaller(mockRepo)
	installedNodes, err := ndi.Install(context.Background(), plugin, giveNodeDefinitions)

	require.NoError(t, err)
	assert.Len(t, installedNodes, 3)

	// 验证所有节点都被激活
	for _, node := range installedNodes {
		assert.True(t, node.Enabled())
	}

	// 验证调用次数
	assert.Len(t, mockRepo.FindByTypeAndVersionCalls(), 3)
	assert.Len(t, mockRepo.FindLatestByTypeCalls(), 3)
	assert.Len(t, mockRepo.SaveCalls(), 3)
}

func TestNodeDefinitionInstaller_Install_EmptyNodeDefinitions(t *testing.T) {
	// 创建测试插件
	plugin, err := NewPluginFromManifest(&PluginManifest{
		Name:    "test-plugin",
		Version: "1.0.0",
	}, "/test/path", false)
	require.NoError(t, err)

	giveNodeDefinitions := []*NodeDefinition{} // 空的节点定义列表

	mockRepo := &MoqNodeDefinitionRepository{}

	ndi := NewNodeDefinitionInstaller(mockRepo)
	installedNodes, err := ndi.Install(context.Background(), plugin, giveNodeDefinitions)

	require.NoError(t, err)
	assert.Empty(t, installedNodes)

	// 验证没有调用任何方法
	assert.Len(t, mockRepo.FindByTypeAndVersionCalls(), 0)
	assert.Len(t, mockRepo.FindLatestByTypeCalls(), 0)
	assert.Len(t, mockRepo.SaveCalls(), 0)
}
