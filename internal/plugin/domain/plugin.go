package domain

import (
	"errors"
	"resflow/internal/common/domain"
	"time"

	"github.com/google/uuid"
)

type Plugin struct {
	domain.AggregateRoot
	name            string
	version         *domain.Version
	author          string
	displayName     string
	description     string
	icon            string
	path            string
	builtin         bool
	enabled         bool
	nodeDefinitions []*NodeDefinition
}

var (
	ErrPluginNameAndVersionRequired = errors.New("plugin name and giveVersion must be set")
	ErrInvalidPluginVersionFormat   = errors.New("invalid plugin giveVersion format")
	ErrNodeDefinitionAlreadyExists  = errors.New("node definition already exists")
)

func NewPluginFromManifest(manifest *PluginManifest, path string, builtin bool) (*Plugin, error) {
	if manifest.Name == "" || manifest.Version == "" {
		return nil, ErrPluginNameAndVersionRequired
	}
	version, err := domain.NewVersion(manifest.Version)
	if err != nil {
		return nil, ErrInvalidPluginVersionFormat
	}
	return &Plugin{
		AggregateRoot:   domain.NewAggregateRoot(uuid.New().String(), time.Now(), time.Now()),
		name:            manifest.Name,
		version:         version,
		author:          manifest.Author,
		displayName:     manifest.DisplayName,
		description:     manifest.Description,
		icon:            manifest.Icon,
		path:            path,
		builtin:         builtin,
		enabled:         true,
		nodeDefinitions: make([]*NodeDefinition, 0),
	}, nil
}

func NewPlugin(id string, createdAt, updatedAt time.Time, name, versionStr, author, displayName, description, icon, path string, builtin, enabled bool) (*Plugin, error) {
	version, err := domain.NewVersion(versionStr)
	if err != nil {
		return nil, ErrInvalidPluginVersionFormat
	}
	return &Plugin{
		AggregateRoot:   domain.NewAggregateRoot(id, createdAt, updatedAt),
		name:            name,
		version:         version,
		author:          author,
		displayName:     displayName,
		description:     description,
		icon:            icon,
		path:            path,
		builtin:         builtin,
		enabled:         enabled,
		nodeDefinitions: make([]*NodeDefinition, 0),
	}, nil
}

func (p *Plugin) Name() string {
	return p.name
}

func (p *Plugin) Version() *domain.Version {
	return p.version
}

func (p *Plugin) Author() string {
	return p.author
}

func (p *Plugin) DisplayName() string {
	return p.displayName
}

func (p *Plugin) Description() string {
	return p.description
}

func (p *Plugin) Icon() string {
	return p.icon
}

func (p *Plugin) Path() string {
	return p.path
}

func (p *Plugin) Builtin() bool {
	return p.builtin
}

func (p *Plugin) Enabled() bool {
	return p.enabled
}

func (p *Plugin) Enable() {
	p.enabled = true
}

func (p *Plugin) NodeDefinitions() []*NodeDefinition {
	nodeDefinitions := make([]*NodeDefinition, 0)
	for _, nodeDefinition := range p.nodeDefinitions {
		nodeDefinitions = append(nodeDefinitions, nodeDefinition)
	}
	return nodeDefinitions
}

func (p *Plugin) AddNodeDefinition(nodeDefinition *NodeDefinition) error {
	for _, nodeDef := range p.NodeDefinitions() {
		if nodeDef.Type() == nodeDefinition.Type() && nodeDef.Version().String() == nodeDefinition.Version().String() {
			return ErrNodeDefinitionAlreadyExists
		}
	}
	p.nodeDefinitions = append(p.nodeDefinitions, nodeDefinition)
	return nil
}
