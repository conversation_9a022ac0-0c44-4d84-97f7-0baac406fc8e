package application

import (
	"context"
	"errors"
	commondomain "resflow/internal/common/domain"
	"resflow/internal/plugin/domain"
	"resflow/utils"
)

var (
	ErrPluginAlreadyInstalled = errors.New("plugin already installed")
)

type PluginInstaller struct {
	scanner            *domain.PluginScanner
	repository         domain.PluginRepository
	manager            *domain.PluginManager
	nodeInstaller      *domain.NodeDefinitionInstaller
	transactionManager commondomain.TransactionManager
}

func NewPluginInstaller(scanner *domain.PluginScanner, repository domain.PluginRepository, manager *domain.PluginManager, nodeInstaller *domain.NodeDefinitionInstaller, transactionManager commondomain.TransactionManager) *PluginInstaller {
	return &PluginInstaller{
		scanner:            scanner,
		repository:         repository,
		manager:            manager,
		nodeInstaller:      nodeInstaller,
		transactionManager: transactionManager,
	}
}

func (pi PluginInstaller) InstallPluginFromDirectory(ctx context.Context, pluginPath string, builtin bool) error {
	plugin, err := pi.scanner.ScanPluginDirectory(pluginPath, builtin)
	if err != nil {
		utils.Logger.Debugf("解析插件清单失败：%s", err.Error())
		return err
	}

	err = pi.transactionManager.WithTx(ctx, func(ctx context.Context) error {
		_, err := pi.nodeInstaller.Install(ctx, plugin, plugin.NodeDefinitions())
		if err != nil {
			utils.Logger.Debugf("安装节点失败：%s", err.Error())
			return err
		}

		return pi.repository.Save(ctx, plugin)
	})

	if err != nil {
		utils.Logger.Debugf("安装插件失败：%s", err.Error())
		return err
	}

	return nil
}
