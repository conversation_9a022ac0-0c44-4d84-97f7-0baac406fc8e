package router

import (
	v1 "resflow/proto/generated_go/v1"
	"resflow/service"
	"resflow/store"

	"google.golang.org/grpc"
)

func SetupGrpcRoutes(s *grpc.Server, store *store.Store) {
	userService := service.NewUserService(store.UserStore)
	tokenService := service.NewTokenService()
	// 认证服务
	v1.RegisterAuthServiceServer(s, service.NewAuthService(userService, tokenService))
	// 用户服务
	v1.RegisterUserServiceServer(s, userService)
	// 工作流服务
	nodesUpdater := service.NewNodeUpdate(store.WorkflowNodeStore)
	linksUpdater := service.NewLinkUpdate(store.WorkflowLinkStore)
	v1.RegisterWorkflowServiceServer(s, service.NewWorkflowService(store.WorkflowStore, store.WorkflowNodeStore, store.WorkflowLinkStore, nodesUpdater, linksUpdater))
	// 插件服务
	//v1.RegisterPluginServiceServer(s, grpcservice.NewPluginGrpcService(plugin.NewPluginStore(database.Client)))
	// 节点服务
	//v1.RegisterNodeDefinitionServiceServer(s, grpcservice.NewNodeDefinitionGrpcService(node_definition.NewService(node_definition.NewNodeDefinitionStore(database.Client))))
}
